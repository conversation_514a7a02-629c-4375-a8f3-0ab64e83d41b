import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Search, MapPin, Star, Clock, ArrowRight, Filter } from "lucide-react";
import { API_BASE_URL } from "../../config/api";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import Loader from "../../components/common/Loader";
import QuickSearch from "../../components/search/QuickSearch";
import { RestaurantGridSkeleton } from "../../components/skeleton/RestaurantCardSkeleton";

const Home = () => {
  const [activeCategory, setActiveCategory] = useState("All");
  const [featuredRestaurants, setFeaturedRestaurants] = useState([]);
  const [allRestaurants, setAllRestaurants] = useState([]);
  const [cuisineCategories, setCuisineCategories] = useState(["All"]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);

  // Fetch restaurants from API
  useEffect(() => {
    const fetchRestaurants = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/restaurant/restaurants/`);

        if (response.ok) {
          const restaurants = await response.json();
          setAllRestaurants(restaurants);

          // Extract unique cuisines for categories
          const cuisines = [...new Set(restaurants.map((r) => r.name))]; // Using name as cuisine for now
          setCuisineCategories(["All", ...cuisines.slice(0, 8)]); // Limit to 8 categories
        } else {
          console.error("Failed to fetch restaurants:", response.status);
        }
      } catch (error) {
        console.error("Error fetching restaurants:", error);
        // Fallback to empty arrays
        setAllRestaurants([]);
        setCuisineCategories(["All"]);
      } finally {
        setLoading(false);
      }
    };

    fetchRestaurants();
  }, []);

  // Filter restaurants based on category and search
  useEffect(() => {
    if (allRestaurants.length === 0) return;

    setLoading(true);
    let filtered = allRestaurants;

    if (activeCategory !== "All") {
      filtered = filtered.filter((restaurant) =>
        restaurant.name.toLowerCase().includes(activeCategory.toLowerCase())
      );
    }

    if (searchQuery) {
      filtered = filtered.filter(
        (restaurant) =>
          restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          restaurant.description
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase())
      );
    }

    // Simulate loading delay
    setTimeout(() => {
      setFeaturedRestaurants(filtered.slice(0, 6)); // Show more restaurants
      setLoading(false);
    }, 300);
  }, [activeCategory, searchQuery, allRestaurants]);

  return (
    <div className='animate-fade-in'>
      {/* Hero Section */}
      <section
        className='relative bg-cover bg-center h-[500px] flex items-center'
        style={{
          backgroundImage:
            "url('https://images.pexels.com/photos/958545/pexels-photo-958545.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1')",
        }}
      >
        <div className='absolute inset-0 bg-black bg-opacity-50'></div>
        <div className='container mx-auto px-4 relative z-10'>
          <div className='max-w-2xl'>
            <h1 className='text-white font-poppins font-bold text-4xl md:text-5xl mb-4'>
              Experience Authentic Afghan Cuisine
            </h1>
            <p className='text-gray-200 text-lg mb-8'>
              Discover the richness of Afghan flavors from the best restaurants,
              delivered right to your door.
            </p>
          </div>
        </div>
      </section>

      {/* Quick Search Component */}
      <section className='container mx-auto relative'>
        <QuickSearch />
      </section>

      {/* Cuisine Categories */}
      <section className='py-10 bg-background-light'>
        <div className='container mx-auto px-4'>
          <h2 className='text-2xl font-poppins font-semibold mb-6'>
            Popular Cuisines
          </h2>

          <div className='flex overflow-x-auto pb-4 hide-scrollbar'>
            <div className='flex space-x-3'>
              {cuisineCategories.map((category) => (
                <button
                  key={category}
                  className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                    activeCategory === category
                      ? "bg-primary-500 text-white"
                      : "bg-white border border-gray-200 text-text-primary hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Restaurants */}
      <section className='py-12 bg-white'>
        <div className='container mx-auto px-4'>
          <div className='flex justify-between items-center mb-8'>
            <h2 className='text-2xl font-poppins font-semibold'>
              Featured Restaurants
            </h2>
            <Link
              to='/restaurants'
              className='text-primary-500 hover:text-primary-600 flex items-center'
            >
              View All <ArrowRight size={16} className='ml-1' />
            </Link>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {loading ? (
              <RestaurantGridSkeleton count={3} />
            ) : featuredRestaurants.length === 0 ? (
              <div className='col-span-3 text-center text-text-secondary py-12'>
                No restaurants found for this category or search.
              </div>
            ) : (
              featuredRestaurants.map((restaurant) => (
                <Link to={`/restaurants/${restaurant.id}`} key={restaurant.id}>
                  <Card
                    className='h-full transition-transform duration-200 hover:-translate-y-1'
                    hoverable
                  >
                    <div className='relative h-48 rounded-t-lg overflow-hidden -mx-5 -mt-5 mb-4'>
                      <div className='absolute top-4 left-4 z-10'>
                        {restaurant.is_active ? (
                          <Badge variant='success' size='small'>
                            Open Now
                          </Badge>
                        ) : (
                          <Badge variant='danger' size='small'>
                            Closed
                          </Badge>
                        )}
                      </div>
                      <img
                        src={restaurant.banner || "/placeholder-restaurant.jpg"}
                        alt={restaurant.name}
                        className='w-full h-full object-cover'
                        onError={(e) => {
                          e.target.src = "/placeholder-restaurant.jpg";
                        }}
                      />
                    </div>

                    <div className='flex items-start'>
                      <div className='w-16 h-16 rounded-lg overflow-hidden bg-gray-100 mr-4 flex-shrink-0'>
                        <img
                          src={restaurant.logo || "/placeholder-logo.jpg"}
                          alt={restaurant.name}
                          className='w-full h-full object-cover'
                          onError={(e) => {
                            e.target.src = "/placeholder-logo.jpg";
                          }}
                        />
                      </div>

                      <div>
                        <h3 className='font-semibold text-lg'>
                          {restaurant.name}
                        </h3>
                        <div className='flex items-center mt-1 text-text-secondary text-sm'>
                          <Star size={16} className='text-yellow-500 mr-1' />
                          <span>{restaurant.rating}</span>
                          <span className='mx-2'>•</span>
                          <span>
                            $$ •{" "}
                            {restaurant.cuisine_types &&
                            restaurant.cuisine_types.length > 0
                              ? restaurant.cuisine_types.join(", ")
                              : "Afghan Cuisine"}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className='mt-4 flex items-center justify-between text-sm text-text-secondary'>
                      <div className='flex items-center'>
                        <Clock size={16} className='mr-1' />
                        <span>
                          {restaurant.average_preparation_time || 30} min
                        </span>
                      </div>
                      <div className='flex items-center'>
                        <MapPin size={16} className='mr-1' />
                        <span>2.5 km</span>
                      </div>
                      <div>
                        $
                        {restaurant.delivery_fee
                          ? parseFloat(restaurant.delivery_fee).toFixed(2)
                          : "0.00"}{" "}
                        delivery
                      </div>
                    </div>
                  </Card>
                </Link>
              ))
            )}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className='py-16 bg-background-light'>
        <div className='container mx-auto px-4'>
          <div className='text-center max-w-3xl mx-auto mb-12'>
            <h2 className='text-3xl font-poppins font-semibold mb-4'>
              How Afghan Sofra Works
            </h2>
            <p className='text-text-secondary'>
              Enjoy authentic Afghan cuisine in just a few simple steps.
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
            <div className='text-center'>
              <div className='w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Search size={24} className='text-primary-500' />
              </div>
              <h3 className='font-poppins font-semibold text-lg mb-2'>
                Find Restaurants
              </h3>
              <p className='text-text-secondary'>
                Browse restaurants offering authentic Afghan cuisine in your
                area.
              </p>
            </div>

            <div className='text-center'>
              <div className='w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Filter size={24} className='text-primary-500' />
              </div>
              <h3 className='font-poppins font-semibold text-lg mb-2'>
                Select Your Meal
              </h3>
              <p className='text-text-secondary'>
                Choose from a variety of traditional Afghan dishes from your
                favorite restaurant.
              </p>
            </div>

            <div className='text-center'>
              <div className='w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <MapPin size={24} className='text-primary-500' />
              </div>
              <h3 className='font-poppins font-semibold text-lg mb-2'>
                Get It Delivered
              </h3>
              <p className='text-text-secondary'>
                Your delicious meal is prepared and delivered right to your
                doorstep.
              </p>
            </div>
          </div>

          <div className='text-center mt-12'>
            <Button to='/restaurants' variant='primary' size='large'>
              Order Now
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className='py-16 bg-primary-500'>
        <div className='container mx-auto px-4'>
          <div className='flex flex-col md:flex-row items-center justify-between'>
            <div className='mb-8 md:mb-0 max-w-xl'>
              <h2 className='text-white font-poppins font-bold text-3xl mb-4'>
                Are you a Restaurant Owner?
              </h2>
              <p className='text-white text-opacity-90 mb-6'>
                Partner with Afghan Sofra to reach more customers and grow your
                business. Our platform helps you manage orders, menu, and
                deliveries with ease.
              </p>
              <Button
                variant='outline'
                size='large'
                className='border-white text-white hover:bg-white hover:text-primary-500'
                to='/restaurant-partner'
              >
                Join as Partner
              </Button>
            </div>

            <div className='w-full md:w-1/3 rounded-lg overflow-hidden shadow-lg'>
              <img
                src='https://images.pexels.com/photos/2696064/pexels-photo-2696064.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
                alt='Restaurant Partner'
                className='w-full h-64 object-cover'
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
