# Dependencies
Frontend/node_modules/
Backend/__pycache__/
Backend/*.pyc
Backend/*.pyo
Backend/*.pyd
Backend/.Python
Backend/env/
Backend/venv/
Backend/ENV/
Backend/env.bak/
Backend/venv.bak/

# Build outputs
Frontend/dist/
Frontend/build/
Backend/staticfiles/
# Backend/media/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Database
# Backend/db.sqlite3
# Backend/*.sqlite3

# Django
# Backend/migrations/
# Backend/*/migrations/
# !Backend/*/migrations/__init__.py

# Cypress
Frontend/cypress/videos/
Frontend/cypress/screenshots/

# Temporary files
*.tmp
*.temp
bash.exe.stackdump
*.local
dist-ssr
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
