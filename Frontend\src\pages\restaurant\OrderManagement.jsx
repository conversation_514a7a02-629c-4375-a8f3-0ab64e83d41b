import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import {
  Clock,
  Package,
  CheckCircle,
  DollarSign,
  Search,
  RefreshCw,
  Eye,
  X,
} from "lucide-react";

function OrderManagement() {
  const { user } = useAuth();

  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("date");
  const [sortOrder, setSortOrder] = useState("desc");

  // Load orders from API
  useEffect(() => {
    const loadOrders = async () => {
      // Check authentication before making API call
      if (!user || !user.access_token || user.role !== "restaurant") {
        console.log("OrderManagement: User not authenticated or not restaurant role");
        return;
      }

      try {
        console.log("Loading orders for restaurant user:", user.name);

        const response = await fetch(
          "http://127.0.0.1:8000/api/order/orders/",
          {
            headers: {
              Authorization: `Bearer ${user.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const ordersData = await response.json();
          console.log("Orders loaded:", ordersData.length);
          setOrders(ordersData);
        } else {
          console.error("Failed to load orders:", response.status);
          if (response.status === 401) {
            console.log("OrderManagement: 401 Unauthorized - clearing user data");
            localStorage.removeItem("afghanSofraUser");
          }
        }
      } catch (error) {
        console.error("Error loading orders:", error);
      } finally {
        setLoading(false);
      }
    } else {
      setLoading(false);
    }
  };

    loadOrders();
  }, [user]);

  // Filter and search functionality
  useEffect(() => {
    let filtered = [...orders];

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((order) => order.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (order) =>
          order.id.toString().toLowerCase().includes(query) ||
          (order.customer?.name &&
            order.customer.name.toLowerCase().includes(query)) ||
          (order.customer?.phone && order.customer.phone.includes(query)) ||
          (order.customer && order.customer.toString().includes(query)) ||
          (order.special_instructions &&
            order.special_instructions.toLowerCase().includes(query))
      );
    }

    // Sort orders
    filtered.sort((a, b) => {
      if (sortBy === "date") {
        const dateA = new Date(a.created_at);
        const dateB = new Date(b.created_at);
        return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
      } else if (sortBy === "amount") {
        const amountA = parseFloat(a.total_amount);
        const amountB = parseFloat(b.total_amount);
        return sortOrder === "asc" ? amountA - amountB : amountB - amountA;
      }
      return 0;
    });

    setFilteredOrders(filtered);
  }, [orders, statusFilter, searchQuery, sortBy, sortOrder]);

  // Refresh orders
  const handleRefreshOrders = async () => {
    setRefreshing(true);
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          "http://127.0.0.1:8000/api/order/orders/",
          {
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const ordersData = await response.json();
          setOrders(ordersData);
        }
      }
    } catch (error) {
      console.error("Error refreshing orders:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Update order status
  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          `http://127.0.0.1:8000/api/order/orders/${orderId}/`,
          {
            method: "PATCH",
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ status: newStatus }),
          }
        );

        if (response.ok) {
          // Refresh orders to get updated data
          handleRefreshOrders();
        }
      }
    } catch (error) {
      console.error("Error updating order status:", error);
    }
  };

  const getStatusBadge = (status) => {
    const statusColors = {
      pending: "bg-yellow-100 text-yellow-800",
      confirmed: "bg-blue-100 text-blue-800",
      preparing: "bg-orange-100 text-orange-800",
      ready: "bg-green-100 text-green-800",
      delivered: "bg-gray-100 text-gray-800",
      cancelled: "bg-red-100 text-red-800",
    };

    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          statusColors[status] || "bg-gray-100 text-gray-800"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className='p-6'>
        <h1 className='text-2xl font-bold mb-4'>Restaurant Order Management</h1>
        <p>Loading orders...</p>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <div className='bg-white shadow-sm border-b border-gray-200'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center py-6'>
            <div>
              <h1 className='text-3xl font-bold text-gray-900'>
                Order Management System
              </h1>
              <p className='mt-1 text-sm text-gray-600'>
                Professional restaurant order management and kitchen display
              </p>
            </div>
            <div className='flex items-center space-x-4'>
              <button
                onClick={handleRefreshOrders}
                disabled={refreshing}
                className='flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50'
              >
                <RefreshCw
                  size={16}
                  className={refreshing ? "animate-spin" : ""}
                />
                <span>{refreshing ? "Refreshing..." : "Refresh"}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
        {/* Stats Cards */}
        <div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
          <div className='bg-white rounded-lg shadow p-6'>
            <div className='flex items-center'>
              <div className='flex-shrink-0'>
                <Package className='h-8 w-8 text-blue-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>
                  Total Orders
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {orders.length}
                </p>
              </div>
            </div>
          </div>

          <div className='bg-white rounded-lg shadow p-6'>
            <div className='flex items-center'>
              <div className='flex-shrink-0'>
                <Clock className='h-8 w-8 text-yellow-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>Pending</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {orders.filter((o) => o.status === "pending").length}
                </p>
              </div>
            </div>
          </div>

          <div className='bg-white rounded-lg shadow p-6'>
            <div className='flex items-center'>
              <div className='flex-shrink-0'>
                <CheckCircle className='h-8 w-8 text-green-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>Completed</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {orders.filter((o) => o.status === "delivered").length}
                </p>
              </div>
            </div>
          </div>

          <div className='bg-white rounded-lg shadow p-6'>
            <div className='flex items-center'>
              <div className='flex-shrink-0'>
                <DollarSign className='h-8 w-8 text-green-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>Revenue</p>
                <p className='text-2xl font-bold text-gray-900'>
                  $
                  {orders
                    .reduce(
                      (sum, order) => sum + parseFloat(order.total_amount),
                      0
                    )
                    .toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className='bg-white rounded-lg shadow mb-6'>
          <div className='p-6'>
            <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
              {/* Search */}
              <div className='relative'>
                <Search
                  className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
                  size={20}
                />
                <input
                  type='text'
                  placeholder='Search orders...'
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className='pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                />
              </div>

              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className='px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              >
                <option value='all'>All Status</option>
                <option value='pending'>Pending</option>
                <option value='confirmed'>Confirmed</option>
                <option value='preparing'>Preparing</option>
                <option value='ready'>Ready</option>
                <option value='delivered'>Delivered</option>
                <option value='cancelled'>Cancelled</option>
              </select>

              {/* Sort By */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className='px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              >
                <option value='date'>Sort by Date</option>
                <option value='amount'>Sort by Amount</option>
              </select>

              {/* Sort Order */}
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value)}
                className='px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              >
                <option value='desc'>Newest First</option>
                <option value='asc'>Oldest First</option>
              </select>
            </div>

            {/* Results Summary */}
            <div className='mt-4 flex justify-between items-center text-sm text-gray-600'>
              <span>
                Showing {filteredOrders.length} of {orders.length} orders
              </span>
            </div>
          </div>
        </div>

        {/* Orders Table */}
        {filteredOrders.length > 0 ? (
          <div className='bg-white shadow rounded-lg overflow-hidden'>
            <div className='overflow-x-auto'>
              <table className='min-w-full divide-y divide-gray-200'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Order Details
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Customer
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Date & Time
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Amount
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Status
                    </th>
                    <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {filteredOrders.map((order) => (
                    <tr
                      key={order.id}
                      className='hover:bg-gray-50 transition-colors duration-200'
                    >
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <div className='flex-shrink-0 h-10 w-10'>
                            <div className='h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center'>
                              <Package className='h-5 w-5 text-blue-600' />
                            </div>
                          </div>
                          <div className='ml-4'>
                            <div className='text-sm font-medium text-gray-900'>
                              Order #{order.id}
                            </div>
                            <div className='text-sm text-gray-500'>
                              {order.items?.length || 0} items
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm font-medium text-gray-900'>
                          {order.customer?.name ||
                            `Customer #${order.customer}`}
                        </div>
                        {order.customer?.phone && (
                          <div className='text-sm text-gray-500'>
                            {order.customer.phone}
                          </div>
                        )}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm text-gray-900'>
                          {formatDate(order.created_at)}
                        </div>
                        <div className='text-xs text-gray-500'>
                          {formatTime(order.created_at)}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm font-medium text-gray-900'>
                          ${parseFloat(order.total_amount).toFixed(2)}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        {getStatusBadge(order.status)}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                        <div className='flex items-center justify-end space-x-2'>
                          <button
                            onClick={() => setSelectedOrder(order)}
                            className='flex items-center space-x-1 px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50'
                          >
                            <Eye size={14} />
                            <span>View</span>
                          </button>

                          {order.status === "pending" && (
                            <button
                              onClick={() =>
                                handleUpdateOrderStatus(order.id, "confirmed")
                              }
                              className='flex items-center space-x-1 px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700'
                            >
                              <CheckCircle size={14} />
                              <span>Accept</span>
                            </button>
                          )}

                          {order.status === "confirmed" && (
                            <button
                              onClick={() =>
                                handleUpdateOrderStatus(order.id, "preparing")
                              }
                              className='flex items-center space-x-1 px-3 py-1 text-sm bg-orange-600 text-white rounded hover:bg-orange-700'
                            >
                              <Clock size={14} />
                              <span>Prepare</span>
                            </button>
                          )}

                          {order.status === "preparing" && (
                            <button
                              onClick={() =>
                                handleUpdateOrderStatus(order.id, "ready")
                              }
                              className='flex items-center space-x-1 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700'
                            >
                              <CheckCircle size={14} />
                              <span>Ready</span>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className='bg-white rounded-lg shadow p-12 text-center'>
            <div className='text-gray-400 mb-4'>
              <Package size={64} className='mx-auto' />
            </div>
            <h3 className='text-xl font-medium text-gray-900 mb-2'>
              No orders found
            </h3>
            <p className='text-gray-600 mb-6'>
              {searchQuery || statusFilter !== "all"
                ? "Try adjusting your filters to see more orders."
                : "Orders from customers will appear here when they place orders."}
            </p>
            {(searchQuery || statusFilter !== "all") && (
              <button
                onClick={() => {
                  setSearchQuery("");
                  setStatusFilter("all");
                }}
                className='px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50'
              >
                Clear Filters
              </button>
            )}
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {selectedOrder && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto'>
            <div className='flex justify-between items-center mb-4'>
              <h2 className='text-xl font-bold'>
                Order Details #{selectedOrder.id}
              </h2>
              <button
                onClick={() => setSelectedOrder(null)}
                className='text-gray-400 hover:text-gray-600'
              >
                <X size={24} />
              </button>
            </div>

            <div className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Status
                  </label>
                  <div className='mt-1'>
                    {getStatusBadge(selectedOrder.status)}
                  </div>
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Total Amount
                  </label>
                  <p className='mt-1 text-lg font-semibold'>
                    ${parseFloat(selectedOrder.total_amount).toFixed(2)}
                  </p>
                </div>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700'>
                  Order Date
                </label>
                <p className='mt-1'>
                  {formatDate(selectedOrder.created_at)} at{" "}
                  {formatTime(selectedOrder.created_at)}
                </p>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700'>
                  Customer
                </label>
                <p className='mt-1'>
                  {selectedOrder.customer?.name ||
                    `Customer #${selectedOrder.customer}`}
                  {selectedOrder.customer?.phone && (
                    <span className='text-gray-500 ml-2'>
                      ({selectedOrder.customer.phone})
                    </span>
                  )}
                </p>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700'>
                  Items
                </label>
                <div className='mt-1 space-y-2'>
                  {selectedOrder.items?.map((item, index) => (
                    <div
                      key={index}
                      className='flex justify-between items-center p-3 bg-gray-50 rounded'
                    >
                      <div>
                        <p className='font-medium'>
                          {item.menu_item?.name || "Item"}
                        </p>
                        <p className='text-sm text-gray-600'>
                          Quantity: {item.quantity}
                        </p>
                      </div>
                      <p className='font-semibold'>
                        $
                        {parseFloat(
                          item.price_at_order || item.price || 0
                        ).toFixed(2)}
                      </p>
                    </div>
                  )) || (
                    <p className='text-gray-500'>No item details available</p>
                  )}
                </div>
              </div>

              {selectedOrder.special_instructions && (
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Special Instructions
                  </label>
                  <p className='mt-1 p-3 bg-gray-50 rounded'>
                    {selectedOrder.special_instructions}
                  </p>
                </div>
              )}
            </div>

            <div className='mt-6 flex justify-end space-x-3'>
              <button
                onClick={() => setSelectedOrder(null)}
                className='px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50'
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default OrderManagement;
