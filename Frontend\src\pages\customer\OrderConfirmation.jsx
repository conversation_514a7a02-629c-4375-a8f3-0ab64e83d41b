import React, { useState, useEffect } from "react";
import { useNavigate, usePara<PERSON>, Link } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { useOrder } from "../../context/OrderContext";
import { useCart } from "../../context/CartContext";
import {
  CheckCircle,
  Clock,
  MapPin,
  CreditCard,
  ChevronRight,
  ArrowRight,
  Star,
  Package,
  RefreshCw,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import { useReviews } from "../../context/ReviewsContext";

const OrderConfirmation = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { getOrderById } = useOrder();
  const { clearCart } = useCart();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [countdown, setCountdown] = useState(10);
  const [retryCount, setRetryCount] = useState(0);
  const [cartCleared, setCartCleared] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewRating, setReviewRating] = useState(0);
  const [reviewText, setReviewText] = useState("");
  const [reviewSubmitted, setReviewSubmitted] = useState(false);
  const { addReview, addAgentReview } = useReviews();
  const [showAgentReviewModal, setShowAgentReviewModal] = useState(false);
  const [agentReviewRating, setAgentReviewRating] = useState(0);
  const [agentReviewText, setAgentReviewText] = useState("");
  const [agentReviewSubmitted, setAgentReviewSubmitted] = useState(false);

  // Fetch order from API
  useEffect(() => {
    const fetchOrder = async () => {
      console.log("🔍 OrderConfirmation: Starting fetch for order ID:", id);
      console.log("🔍 OrderConfirmation: User:", user?.username);

      if (!id || !user) {
        console.log(
          "❌ OrderConfirmation: Missing ID or user, redirecting to orders"
        );
        navigate("/orders");
        return;
      }

      try {
        console.log("🔍 OrderConfirmation: Calling getOrderById with ID:", id);
        const result = await getOrderById(id);

        console.log("🔍 OrderConfirmation: API result:", result);

        if (result.success) {
          console.log(
            "✅ OrderConfirmation: Order data fetched successfully:",
            result.data
          );
          setOrder(result.data);
          setRetryCount(0); // Reset retry count on success

          // Clear cart after successfully loading order confirmation
          if (!cartCleared) {
            setTimeout(() => {
              clearCart();
              setCartCleared(true);
              console.log(
                "🧹 Cart cleared after order confirmation loaded successfully"
              );
            }, 1000); // Clear cart 1 second after order confirmation loads
          }
        } else {
          console.error(
            "❌ OrderConfirmation: Failed to fetch order:",
            result.error
          );

          // Retry up to 3 times with increasing delays for newly created orders
          if (retryCount < 3) {
            console.log(
              `🔄 Retrying order fetch (attempt ${retryCount + 1}/3)...`
            );
            setTimeout(() => {
              setRetryCount((prev) => prev + 1);
              fetchOrder();
            }, (retryCount + 1) * 1000); // 1s, 2s, 3s delays
            return; // Don't set loading to false yet
          }

          // After all retries failed, show "Order Not Found"
          setOrder(null);
        }
      } catch (error) {
        console.error("Error fetching order:", error);
        navigate("/orders");
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [id, navigate, user, getOrderById, clearCart, cartCleared, retryCount]);

  // Countdown timer
  useEffect(() => {
    if (!loading && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (countdown === 0) {
      // Make sure we're using the correct route format
      console.log(
        "Countdown complete, navigating to order tracking with ID:",
        id
      );

      // Add a small delay to ensure state is fully updated
      setTimeout(() => {
        navigate(`/orders/${id}`);
      }, 100);
    }
  }, [countdown, loading, navigate, id]);

  // For debugging - log the order ID
  useEffect(() => {
    console.log("Order ID for tracking:", id);

    // Validate the order ID
    if (!id) {
      console.error("Invalid order ID:", id);
    }
  }, [id]);

  useEffect(() => {
    if (!loading && order) {
      // Show review modal after a short delay (simulate after delivery)
      setTimeout(() => setShowReviewModal(true), 1200);
    }
  }, [loading, order]);

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500'></div>
      </div>
    );
  }

  const orderTime = new Date(order.created_at || order.createdAt);
  const estimatedTime =
    order.estimated_delivery_time || order.estimatedDeliveryTime
      ? new Date(order.estimated_delivery_time || order.estimatedDeliveryTime)
      : null;

  // Review submit handler
  const handleReviewSubmit = (e) => {
    e.preventDefault();
    // Add review to context
    addReview(order.restaurantId, {
      id: Date.now(),
      user: "Customer", // Replace with real user if available
      rating: reviewRating,
      text: reviewText,
      date: new Date().toISOString().slice(0, 10),
    });
    setReviewSubmitted(true);
    setTimeout(() => {
      setShowReviewModal(false);
      setReviewSubmitted(false);
      setReviewRating(0);
      setReviewText("");
      // If order has a deliveryAgentId, show agent review modal
      if (order.deliveryAgentId) {
        setShowAgentReviewModal(true);
      }
    }, 2000);
  };

  // Delivery agent review submit handler
  const handleAgentReviewSubmit = (e) => {
    e.preventDefault();
    addAgentReview(order.deliveryAgentId, {
      id: Date.now(),
      user: "Customer",
      rating: agentReviewRating,
      text: agentReviewText,
      date: new Date().toISOString().slice(0, 10),
    });
    setAgentReviewSubmitted(true);
    setTimeout(() => {
      setShowAgentReviewModal(false);
      setAgentReviewSubmitted(false);
      setAgentReviewRating(0);
      setAgentReviewText("");
    }, 2000);
  };

  if (loading) {
    return (
      <div className='container mx-auto px-4 py-8 max-w-4xl'>
        <div className='text-center'>
          <div className='w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6'>
            <RefreshCw size={40} className='text-blue-600 animate-spin' />
          </div>
          <h1 className='text-2xl font-bold mb-2'>Loading Order Details...</h1>
          <p className='text-gray-600'>
            Please wait while we fetch your order information.
          </p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className='container mx-auto px-4 py-8 max-w-4xl'>
        <div className='text-center'>
          <div className='w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6'>
            <Package size={40} className='text-red-600' />
          </div>
          <h1 className='text-2xl font-bold mb-2'>Order Not Found</h1>
          <p className='text-gray-600 mb-6'>
            We couldn't find the order you're looking for.
          </p>
          <Button variant='primary' to='/orders'>
            View All Orders
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8 max-w-4xl animate-fade-in'>
      <div className='text-center mb-8'>
        <div className='w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6'>
          <CheckCircle size={40} className='text-green-600' />
        </div>
        <h1 className='text-2xl md:text-3xl font-poppins font-bold mb-2'>
          Order Confirmed!
        </h1>
        <p className='text-text-secondary mb-2'>
          Your order has been successfully placed and is being processed.
        </p>
        <p className='text-sm text-primary-500'>
          Redirecting to order tracking in {countdown} seconds...
        </p>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        <div className='lg:col-span-2'>
          <Card className='mb-6'>
            <div className='flex justify-between items-center mb-6'>
              <div>
                <h3 className='font-medium text-text-secondary'>
                  Order #{order.id}
                </h3>
                <h2 className='text-xl font-semibold'>
                  {order.restaurant?.name ||
                    order.restaurantName ||
                    "Restaurant"}
                </h2>
              </div>
              <div className='bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium'>
                {order.status
                  ? order.status.charAt(0).toUpperCase() + order.status.slice(1)
                  : "Processing"}
              </div>
            </div>

            <div className='mb-6'>
              <div className='flex items-center mb-4'>
                <Clock size={20} className='text-text-secondary mr-2' />
                <div>
                  <p className='text-sm text-text-secondary'>Order placed on</p>
                  <p className='font-medium'>
                    {orderTime.toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </p>
                </div>
              </div>

              {estimatedTime && (
                <div className='flex items-center'>
                  <Clock size={20} className='text-text-secondary mr-2' />
                  <div>
                    <p className='text-sm text-text-secondary'>
                      Estimated delivery by
                    </p>
                    <p className='font-medium'>
                      {estimatedTime.toLocaleTimeString("en-US", {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </p>
                  </div>
                </div>
              )}
            </div>

            <div className='mb-6'>
              <h3 className='font-semibold mb-4'>What happens next?</h3>

              <div className='space-y-4'>
                <div className='flex'>
                  <div className='w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center mr-3 flex-shrink-0'>
                    <span className='text-primary-600 font-medium'>1</span>
                  </div>
                  <div>
                    <h4 className='font-medium'>Restaurant Confirmation</h4>
                    <p className='text-sm text-text-secondary'>
                      The restaurant will confirm your order shortly.
                    </p>
                  </div>
                </div>

                <div className='flex'>
                  <div className='w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center mr-3 flex-shrink-0'>
                    <span className='text-primary-600 font-medium'>2</span>
                  </div>
                  <div>
                    <h4 className='font-medium'>Food Preparation</h4>
                    <p className='text-sm text-text-secondary'>
                      Your food will be freshly prepared.
                    </p>
                  </div>
                </div>

                <div className='flex'>
                  <div className='w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center mr-3 flex-shrink-0'>
                    <span className='text-primary-600 font-medium'>3</span>
                  </div>
                  <div>
                    <h4 className='font-medium'>Delivery</h4>
                    <p className='text-sm text-text-secondary'>
                      A delivery agent will pick up your order and bring it to
                      you.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className='flex items-start mb-4'>
              <MapPin size={20} className='text-text-secondary mr-2 mt-0.5' />
              <div>
                <p className='font-medium'>Delivery Address</p>
                <p className='text-text-secondary'>
                  {order.delivery_address?.street_address ||
                    order.deliveryAddress ||
                    "Address not available"}
                </p>
              </div>
            </div>

            <div className='flex items-start'>
              <CreditCard
                size={20}
                className='text-text-secondary mr-2 mt-0.5'
              />
              <div>
                <p className='font-medium'>Payment Method</p>
                <p className='text-text-secondary'>
                  {order.payment_method === "cash_on_delivery"
                    ? "Cash on Delivery"
                    : order.payment_method ||
                      order.paymentMethod ||
                      "Cash on Delivery"}
                </p>
              </div>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-6'>
            <Button
              variant='outline'
              to='/restaurants'
              icon={<ChevronRight size={16} className='ml-1' />}
              iconPosition='right'
              className='w-full'
            >
              Browse More Restaurants
            </Button>

            <Button
              variant='outline'
              to='/orders'
              icon={<Package size={16} className='ml-1' />}
              iconPosition='right'
              className='w-full'
            >
              View All Orders
            </Button>

            <Button
              variant='primary'
              onClick={() => {
                console.log("Navigating to order tracking with ID:", id);
                navigate(`/orders/${id}`);
              }}
              icon={<ArrowRight size={16} />}
              iconPosition='right'
              className='w-full'
            >
              Track This Order
            </Button>
          </div>

          {/* Quick Actions */}
          <div className='mt-6 p-4 bg-gray-50 rounded-lg'>
            <h4 className='font-medium mb-3'>Quick Actions</h4>
            <div className='grid grid-cols-2 gap-3'>
              <button
                onClick={() => setCountdown(0)}
                className='flex items-center justify-center space-x-2 px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-white transition-colors'
              >
                <ArrowRight size={14} />
                <span>Go to Tracking Now</span>
              </button>
              <button
                onClick={() => window.print()}
                className='flex items-center justify-center space-x-2 px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-white transition-colors'
              >
                <Package size={14} />
                <span>Print Receipt</span>
              </button>
            </div>
          </div>

          {/* Rating Information */}
          <div className='mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200'>
            <div className='flex items-start space-x-3'>
              <div className='flex-shrink-0'>
                <span className='text-2xl'>⭐</span>
              </div>
              <div>
                <h4 className='font-medium text-blue-900 mb-1'>
                  Rate Your Experience
                </h4>
                <p className='text-sm text-blue-700'>
                  Once your order is delivered, you'll be able to rate your
                  experience and help other customers by sharing your feedback.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div>
          <Card>
            <h3 className='font-poppins font-semibold text-lg mb-4'>
              Order Summary
            </h3>

            <div className='space-y-3 mb-4'>
              {(order.items || order.orderItems || []).map((item, index) => (
                <div key={item.id || index} className='flex justify-between'>
                  <span>
                    {item.quantity}x{" "}
                    {item.menu_item?.name || item.name || "Item"}
                  </span>
                  <span>
                    $
                    {(
                      Number(item.price || item.menu_item?.price || 0) *
                      item.quantity
                    ).toFixed(2)}
                  </span>
                </div>
              ))}
            </div>

            <div className='border-t border-gray-100 pt-4 mt-4 space-y-3 text-sm'>
              <div className='flex justify-between'>
                <span className='text-text-secondary'>Subtotal</span>
                <span>
                  $
                  {(
                    Number(order.total_amount || order.totalAmount || 0) -
                    Number(order.delivery_fee || order.deliveryFee || 0)
                  ).toFixed(2)}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-text-secondary'>Delivery Fee</span>
                <span>
                  $
                  {Number(order.delivery_fee || order.deliveryFee || 0).toFixed(
                    2
                  )}
                </span>
              </div>
              <div className='pt-3 border-t border-gray-100 flex justify-between font-semibold text-base'>
                <span>Total</span>
                <span>
                  $
                  {Number(order.total_amount || order.totalAmount || 0).toFixed(
                    2
                  )}
                </span>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Review Modal */}
      {showReviewModal && (
        <div className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg max-w-md w-full p-8 animate-fade-in'>
            {reviewSubmitted ? (
              <div className='text-center py-8'>
                <CheckCircle
                  size={48}
                  className='mx-auto text-green-500 mb-4'
                />
                <h3 className='text-xl font-semibold mb-2'>
                  Thank you for your review!
                </h3>
                <p className='text-text-secondary'>
                  Your feedback helps us improve our service.
                </p>
              </div>
            ) : (
              <form onSubmit={handleReviewSubmit}>
                <h3 className='text-xl font-semibold mb-4 text-center'>
                  Rate Your Restaurant Experience
                </h3>
                <div className='flex justify-center mb-4'>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      type='button'
                      key={star}
                      onClick={() => setReviewRating(star)}
                      className='focus:outline-none'
                    >
                      <Star
                        size={32}
                        className={
                          star <= reviewRating
                            ? "text-yellow-400"
                            : "text-gray-300"
                        }
                      />
                    </button>
                  ))}
                </div>
                <textarea
                  className='w-full border border-gray-200 rounded-md p-3 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-500'
                  rows={3}
                  placeholder='Write a review (optional)'
                  value={reviewText}
                  onChange={(e) => setReviewText(e.target.value)}
                />
                <Button
                  type='submit'
                  variant='primary'
                  fullWidth
                  disabled={reviewRating === 0}
                >
                  Submit Review
                </Button>
                <Button
                  type='button'
                  variant='outline'
                  fullWidth
                  className='mt-2'
                  onClick={() => setShowReviewModal(false)}
                >
                  Skip
                </Button>
              </form>
            )}
          </div>
        </div>
      )}
      {/* Delivery Agent Review Modal */}
      {showAgentReviewModal && (
        <div className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg max-w-md w-full p-8 animate-fade-in'>
            {agentReviewSubmitted ? (
              <div className='text-center py-8'>
                <CheckCircle
                  size={48}
                  className='mx-auto text-green-500 mb-4'
                />
                <h3 className='text-xl font-semibold mb-2'>
                  Thank you for rating your delivery agent!
                </h3>
                <p className='text-text-secondary'>
                  Your feedback helps us improve our service.
                </p>
              </div>
            ) : (
              <form onSubmit={handleAgentReviewSubmit}>
                <h3 className='text-xl font-semibold mb-4 text-center'>
                  Rate Your Delivery Agent
                </h3>
                <div className='flex justify-center mb-4'>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      type='button'
                      key={star}
                      onClick={() => setAgentReviewRating(star)}
                      className='focus:outline-none'
                    >
                      <Star
                        size={32}
                        className={
                          star <= agentReviewRating
                            ? "text-yellow-400"
                            : "text-gray-300"
                        }
                      />
                    </button>
                  ))}
                </div>
                <textarea
                  className='w-full border border-gray-200 rounded-md p-3 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-500'
                  rows={3}
                  placeholder='Write a review (optional)'
                  value={agentReviewText}
                  onChange={(e) => setAgentReviewText(e.target.value)}
                />
                <Button
                  type='submit'
                  variant='primary'
                  fullWidth
                  disabled={agentReviewRating === 0}
                >
                  Submit Review
                </Button>
                <Button
                  type='button'
                  variant='outline'
                  fullWidth
                  className='mt-2'
                  onClick={() => setShowAgentReviewModal(false)}
                >
                  Skip
                </Button>
              </form>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderConfirmation;
