import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import {
  Store,
  Plus,
  ChevronRight,
  MapPin,
  Phone,
  Clock,
  Star,
  AlertCircle,
  ShieldCheck,
  Edit,
  Menu,
  Package,
  Settings,
  Trash2,
  MoreVertical,
  Eye,
} from "lucide-react";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import { DashboardSkeleton } from "../../components/skeleton/DashboardSkeleton";

const MyRestaurants = () => {
  const { user } = useAuth();
  const {
    restaurants,
    getUserRestaurants,
    loading,
    error,
    setCurrentRestaurant,
  } = useRestaurant();
  const navigate = useNavigate();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [restaurantToDelete, setRestaurantToDelete] = useState(null);

  useEffect(() => {
    const fetchRestaurants = async () => {
      // Fetch all restaurants owned by the current user
      await getUserRestaurants();
    };

    if (user && user.role === "restaurant") {
      fetchRestaurants();
    }
  }, [user, getUserRestaurants]);

  const handleRestaurantSelect = (restaurant) => {
    setCurrentRestaurant(restaurant);
    navigate(`/restaurant/dashboard/${restaurant.id}`);
  };

  const handleAddNewRestaurant = () => {
    navigate("/register-restaurant");
  };

  const confirmDeleteRestaurant = (restaurant) => {
    setRestaurantToDelete(restaurant);
    setShowDeleteConfirm(true);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setRestaurantToDelete(null);
  };

  const handleDeleteRestaurant = async () => {
    // Implementation for deleting a restaurant would go here
    // For now, just close the modal
    setShowDeleteConfirm(false);
    setRestaurantToDelete(null);
  };

  if (loading) {
    return (
      <div className='p-6 animate-fade-in'>
        <DashboardSkeleton userRole='restaurant' />
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      {/* Header */}
      <div className='mb-8'>
        <h1 className='text-3xl font-bold text-gray-900 mb-2'>
          My Restaurants
        </h1>
        <p className='text-text-secondary'>
          Manage all your restaurants from one place
        </p>
      </div>

      {/* Add New Restaurant Button */}
      <div className='mb-8'>
        <Button
          variant='primary'
          onClick={handleAddNewRestaurant}
          className='flex items-center'
        >
          <Plus className='mr-2' size={18} />
          Add New Restaurant
        </Button>
      </div>

      {/* Restaurant List */}
      {restaurants.length === 0 ? (
        <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center'>
          <div className='w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4'>
            <Store className='text-primary-600' size={28} />
          </div>
          <h2 className='text-xl font-semibold mb-2'>No Restaurants Yet</h2>
          <p className='text-text-secondary mb-6 max-w-md mx-auto'>
            You haven't added any restaurants yet. Create your first restaurant
            to start receiving orders.
          </p>
          <Button
            variant='primary'
            onClick={handleAddNewRestaurant}
            className='flex items-center mx-auto'
          >
            <Plus className='mr-2' size={18} />
            Create Your First Restaurant
          </Button>
        </div>
      ) : (
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {restaurants.map((restaurant) => (
            <Card
              key={restaurant.id}
              className='overflow-hidden hover:shadow-md transition-shadow'
            >
              {/* Restaurant Banner */}
              <div className='h-32 bg-gray-200 relative'>
                {restaurant.banner ? (
                  <img
                    src={restaurant.banner}
                    alt={`${restaurant.name} banner`}
                    className='w-full h-full object-cover'
                  />
                ) : (
                  <div className='w-full h-full bg-gradient-to-r from-primary-100 to-primary-200 flex items-center justify-center'>
                    <Store className='text-primary-600' size={32} />
                  </div>
                )}
                <div className='absolute top-2 right-2'>
                  <Badge
                    variant={restaurant.is_verified ? "success" : "warning"}
                  >
                    {restaurant.is_verified ? "Verified" : "Pending"}
                  </Badge>
                </div>
              </div>

              {/* Restaurant Info */}
              <div className='p-4'>
                <div className='flex items-start'>
                  <div className='w-16 h-16 rounded-full bg-white border-4 border-white shadow-md overflow-hidden -mt-8 mr-3'>
                    {restaurant.logo ? (
                      <img
                        src={restaurant.logo}
                        alt={`${restaurant.name} logo`}
                        className='w-full h-full object-cover'
                      />
                    ) : (
                      <div className='w-full h-full bg-primary-100 flex items-center justify-center'>
                        <Store className='text-primary-600' size={24} />
                      </div>
                    )}
                  </div>
                  <div className='flex-1 -mt-2'>
                    <h3 className='text-lg font-semibold text-gray-900 truncate'>
                      {restaurant.name}
                    </h3>
                    <div className='flex items-center text-sm text-text-secondary mt-1'>
                      <MapPin size={14} className='mr-1' />
                      <span className='truncate'>
                        {restaurant.address?.street || "No address"}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Restaurant Stats */}
                <div className='grid grid-cols-2 gap-4 mt-4'>
                  <div className='bg-gray-50 rounded-lg p-3'>
                    <div className='text-sm text-text-secondary'>Orders</div>
                    <div className='text-lg font-semibold'>
                      {restaurant.total_orders || 0}
                    </div>
                  </div>
                  <div className='bg-gray-50 rounded-lg p-3'>
                    <div className='text-sm text-text-secondary'>Rating</div>
                    <div className='text-lg font-semibold flex items-center'>
                      {restaurant.rating || "N/A"}
                      {restaurant.rating && (
                        <Star
                          size={16}
                          className='ml-1 text-yellow-400 fill-current'
                        />
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className='mt-4 flex flex-col space-y-2'>
                  <Button
                    variant='primary'
                    onClick={() => handleRestaurantSelect(restaurant)}
                    className='w-full'
                  >
                    Manage Restaurant
                  </Button>
                  <div className='grid grid-cols-4 gap-2'>
                    <Button
                      variant='outline'
                      size='small'
                      to={`/restaurant/menu/${restaurant.id}`}
                      className='p-2'
                      title='Menu'
                    >
                      <Menu size={18} />
                    </Button>
                    <Button
                      variant='outline'
                      size='small'
                      to={`/restaurant/orders/${restaurant.id}`}
                      className='p-2'
                      title='Orders'
                    >
                      <Package size={18} />
                    </Button>
                    <Button
                      variant='outline'
                      size='small'
                      to={`/restaurant/edit/${restaurant.id}`}
                      className='p-2'
                      title='Edit'
                    >
                      <Edit size={18} />
                    </Button>
                    <Button
                      variant='outline'
                      size='small'
                      onClick={() => confirmDeleteRestaurant(restaurant)}
                      className='p-2 text-red-500 hover:text-red-600 hover:border-red-300'
                      title='Delete'
                    >
                      <Trash2 size={18} />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg shadow-lg max-w-md w-full p-6'>
            <h3 className='text-xl font-semibold mb-4'>Delete Restaurant</h3>
            <p className='mb-6'>
              Are you sure you want to delete "{restaurantToDelete?.name}"? This
              action cannot be undone.
            </p>
            <div className='flex justify-end space-x-3'>
              <Button variant='outline' onClick={cancelDelete}>
                Cancel
              </Button>
              <Button variant='danger' onClick={handleDeleteRestaurant}>
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MyRestaurants;
