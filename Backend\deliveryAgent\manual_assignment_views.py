"""
Manual Order Assignment Views
Admin manually assigns orders to delivery agents
"""

from django.shortcuts import get_object_or_404
from django.http import JsonResponse
from django.db import transaction
from django.utils import timezone

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from orders.models import Order
from .models import DeliveryAgentProfile
from django.contrib.auth import get_user_model

User = get_user_model()


class AdminOrderAssignmentView(APIView):
    """Admin manually assigns orders to delivery agents"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get orders ready for assignment and available agents"""
        if request.user.role != 'admin':
            return Response({
                'status': 'error',
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        try:
            # Get orders ready for assignment
            ready_orders = Order.objects.filter(
                status='ready',
                delivery_agent__isnull=True
            ).select_related('restaurant', 'customer').order_by('-created_at')
            
            # Get available delivery agents (employees)
            available_agents = DeliveryAgentProfile.objects.filter(
                employment_status='active',
                availability__in=['available'],
                is_clocked_in=True
            ).select_related('user').order_by('agent_id')
            
            # Serialize orders
            orders_data = []
            for order in ready_orders:
                orders_data.append({
                    'id': order.id,
                    'customer_name': order.customer.name,
                    'customer_phone': order.customer.phone,
                    'restaurant_name': order.restaurant.name,
                    'restaurant_address': order.restaurant.address,
                    'delivery_address': order.delivery_address,
                    'total_amount': str(order.total_amount),
                    'payment_method': order.payment_method,
                    'special_instructions': order.special_instructions,
                    'created_at': order.created_at,
                    'estimated_prep_time': 30,  # Default 30 minutes
                    'priority': 'normal'  # Can be enhanced later
                })
            
            # Serialize agents
            agents_data = []
            for agent in available_agents:
                agents_data.append({
                    'id': agent.id,
                    'agent_id': agent.agent_id,
                    'employee_number': agent.employee_number,
                    'full_name': agent.full_name,
                    'phone_number': agent.phone_number,
                    'availability': agent.availability,
                    'current_location': {
                        'latitude': str(agent.current_latitude) if agent.current_latitude else None,
                        'longitude': str(agent.current_longitude) if agent.current_longitude else None,
                        'address': agent.current_address
                    },
                    'vehicle_type': agent.vehicle_type,
                    'total_deliveries': agent.total_deliveries,
                    'rating': str(agent.rating),
                    'completion_rate': agent.completion_rate,
                    'last_active': agent.last_active,
                    'shift_hours': str(agent.current_shift_duration)
                })
            
            return Response({
                'status': 'success',
                'data': {
                    'ready_orders': orders_data,
                    'available_agents': agents_data,
                    'summary': {
                        'total_ready_orders': len(orders_data),
                        'total_available_agents': len(agents_data),
                        'agents_on_duty': DeliveryAgentProfile.objects.filter(
                            employment_status='active',
                            is_clocked_in=True
                        ).count(),
                        'agents_busy': DeliveryAgentProfile.objects.filter(
                            employment_status='active',
                            availability='busy'
                        ).count()
                    }
                }
            })
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Failed to fetch assignment data: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """Manually assign order to delivery agent"""
        if request.user.role != 'admin':
            return Response({
                'status': 'error',
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        try:
            order_id = request.data.get('order_id')
            agent_id = request.data.get('agent_id')
            assignment_notes = request.data.get('notes', '')
            
            if not order_id or not agent_id:
                return Response({
                    'status': 'error',
                    'message': 'Order ID and Agent ID are required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            with transaction.atomic():
                # Get order
                order = get_object_or_404(Order, id=order_id)
                
                # Validate order status
                if order.status != 'ready':
                    return Response({
                        'status': 'error',
                        'message': f'Order is not ready for assignment. Current status: {order.status}'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if order.delivery_agent:
                    return Response({
                        'status': 'error',
                        'message': 'Order is already assigned to another agent'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Get agent
                agent_profile = get_object_or_404(DeliveryAgentProfile, agent_id=agent_id)
                
                # Validate agent availability
                if agent_profile.employment_status != 'active':
                    return Response({
                        'status': 'error',
                        'message': f'Agent is not active. Status: {agent_profile.employment_status}'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if not agent_profile.is_clocked_in:
                    return Response({
                        'status': 'error',
                        'message': 'Agent is not clocked in'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if agent_profile.availability not in ['available']:
                    return Response({
                        'status': 'error',
                        'message': f'Agent is not available. Current status: {agent_profile.availability}'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Perform assignment
                order.delivery_agent = agent_profile.user
                order.status = 'assigned'
                order.assignment_attempts += 1
                order.last_assigned_at = timezone.now()
                order._changed_by = request.user
                order._status_change_notes = f"Manually assigned to {agent_profile.full_name} ({agent_profile.agent_id}) by {request.user.name}. Notes: {assignment_notes}"
                order.save()
                
                # Update agent status
                agent_profile.availability = 'busy'
                agent_profile.last_active = timezone.now()
                agent_profile.save(update_fields=['availability', 'last_active'])
                
                # Log assignment
                from orders.models import AssignmentLog
                AssignmentLog.objects.create(
                    order=order,
                    agent=agent_profile.user,
                    action='assigned'
                )
                
                # Send notification to agent (implement notification system)
                self.send_assignment_notification(order, agent_profile)
                
                return Response({
                    'status': 'success',
                    'message': 'Order assigned successfully',
                    'data': {
                        'order_id': order.id,
                        'agent_id': agent_profile.agent_id,
                        'agent_name': agent_profile.full_name,
                        'assignment_time': order.last_assigned_at,
                        'order_status': order.status,
                        'agent_status': agent_profile.availability,
                        'estimated_delivery_time': 45  # Can be calculated based on distance
                    }
                })
                
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Assignment failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def send_assignment_notification(self, order, agent_profile):
        """Send notification to agent about new assignment"""
        # This can be implemented with WebSocket, SMS, or push notifications
        # For now, we'll just log it
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Assignment notification sent to {agent_profile.full_name} for order {order.id}")


class AdminOrderReassignmentView(APIView):
    """Reassign order to different agent"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Reassign order to different agent"""
        if request.user.role != 'admin':
            return Response({
                'status': 'error',
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        try:
            order_id = request.data.get('order_id')
            new_agent_id = request.data.get('new_agent_id')
            reason = request.data.get('reason', 'Admin reassignment')
            
            if not order_id or not new_agent_id:
                return Response({
                    'status': 'error',
                    'message': 'Order ID and new Agent ID are required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            with transaction.atomic():
                # Get order
                order = get_object_or_404(Order, id=order_id)
                
                # Validate order can be reassigned
                if order.status not in ['assigned', 'picked_up']:
                    return Response({
                        'status': 'error',
                        'message': f'Order cannot be reassigned. Current status: {order.status}'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Get current agent
                current_agent = None
                if order.delivery_agent:
                    try:
                        current_agent = DeliveryAgentProfile.objects.get(user=order.delivery_agent)
                    except DeliveryAgentProfile.DoesNotExist:
                        pass
                
                # Get new agent
                new_agent_profile = get_object_or_404(DeliveryAgentProfile, agent_id=new_agent_id)
                
                # Validate new agent
                if new_agent_profile.employment_status != 'active':
                    return Response({
                        'status': 'error',
                        'message': 'New agent is not active'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if not new_agent_profile.is_clocked_in:
                    return Response({
                        'status': 'error',
                        'message': 'New agent is not clocked in'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if new_agent_profile.availability not in ['available']:
                    return Response({
                        'status': 'error',
                        'message': f'New agent is not available. Status: {new_agent_profile.availability}'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Update current agent status (make available again)
                if current_agent:
                    current_agent.availability = 'available'
                    current_agent.save(update_fields=['availability'])
                
                # Assign to new agent
                order.delivery_agent = new_agent_profile.user
                order.status = 'assigned'  # Reset to assigned
                order.assignment_attempts += 1
                order.last_assigned_at = timezone.now()
                order._changed_by = request.user
                order._status_change_notes = f"Reassigned from {current_agent.full_name if current_agent else 'Unknown'} to {new_agent_profile.full_name}. Reason: {reason}"
                order.save()
                
                # Update new agent status
                new_agent_profile.availability = 'busy'
                new_agent_profile.last_active = timezone.now()
                new_agent_profile.save(update_fields=['availability', 'last_active'])
                
                # Log reassignment
                from orders.models import AssignmentLog
                AssignmentLog.objects.create(
                    order=order,
                    agent=new_agent_profile.user,
                    action='reassigned',
                    notes=f"Reassigned by {request.user.name}. Reason: {reason}"
                )
                
                return Response({
                    'status': 'success',
                    'message': 'Order reassigned successfully',
                    'data': {
                        'order_id': order.id,
                        'previous_agent': current_agent.full_name if current_agent else None,
                        'new_agent_id': new_agent_profile.agent_id,
                        'new_agent_name': new_agent_profile.full_name,
                        'reassignment_time': order.last_assigned_at,
                        'reason': reason
                    }
                })
                
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Reassignment failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdminActiveDeliveriesView(APIView):
    """Monitor active deliveries"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get all active deliveries"""
        if request.user.role != 'admin':
            return Response({
                'status': 'error',
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        try:
            # Get active deliveries
            active_orders = Order.objects.filter(
                status__in=['assigned', 'picked_up', 'on_the_way'],
                delivery_agent__isnull=False
            ).select_related('restaurant', 'customer', 'delivery_agent').order_by('-last_assigned_at')
            
            deliveries_data = []
            for order in active_orders:
                try:
                    agent_profile = DeliveryAgentProfile.objects.get(user=order.delivery_agent)
                    deliveries_data.append({
                        'order_id': order.id,
                        'status': order.status,
                        'customer_name': order.customer.name,
                        'customer_phone': order.customer.phone,
                        'restaurant_name': order.restaurant.name,
                        'delivery_address': order.delivery_address,
                        'total_amount': str(order.total_amount),
                        'payment_method': order.payment_method,
                        'agent': {
                            'agent_id': agent_profile.agent_id,
                            'name': agent_profile.full_name,
                            'phone': agent_profile.phone_number,
                            'availability': agent_profile.availability,
                            'current_location': {
                                'latitude': str(agent_profile.current_latitude) if agent_profile.current_latitude else None,
                                'longitude': str(agent_profile.current_longitude) if agent_profile.current_longitude else None,
                                'address': agent_profile.current_address
                            }
                        },
                        'assigned_at': order.last_assigned_at,
                        'estimated_delivery': None,  # Can be calculated
                        'assignment_attempts': order.assignment_attempts
                    })
                except DeliveryAgentProfile.DoesNotExist:
                    continue
            
            return Response({
                'status': 'success',
                'data': {
                    'active_deliveries': deliveries_data,
                    'summary': {
                        'total_active': len(deliveries_data),
                        'assigned': len([d for d in deliveries_data if d['status'] == 'assigned']),
                        'picked_up': len([d for d in deliveries_data if d['status'] == 'picked_up']),
                        'on_the_way': len([d for d in deliveries_data if d['status'] == 'on_the_way'])
                    }
                }
            })
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Failed to fetch active deliveries: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
