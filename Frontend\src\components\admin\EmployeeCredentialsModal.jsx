import React, { useState } from "react";
import {
  User,
  <PERSON>,
  Copy,
  CheckCircle,
  Eye,
  EyeOff,
  Download,
  Mail,
  Phone,
  Calendar,
  AlertCircle,
} from "lucide-react";
import Button from "../common/Button";
import Card from "../common/Card";

const EmployeeCredentialsModal = ({ isOpen, onClose, employeeData }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [copiedField, setCopiedField] = useState("");

  if (!isOpen || !employeeData) return null;

  const copyToClipboard = async (text, fieldName) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(""), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  const generateCredentialsPDF = () => {
    const content = `
DELIVERY AGENT CREDENTIALS
==========================

Employee Information:
- Full Name: ${employeeData.full_name}
- Agent ID: ${employeeData.agent_id}
- Employee Number: ${employeeData.employee_number}
- Phone: ${employeeData.phone_number}
- Hire Date: ${employeeData.hire_date}

Login Credentials:
- Website: ${
      employeeData.login_instructions?.website || "http://localhost:5173/login"
    }
- Username: ${employeeData.username}
- Temporary Password: ${employeeData.temporary_password}

IMPORTANT INSTRUCTIONS:
1. Employee must change password on first login
2. Keep these credentials secure
3. Contact supervisor if login issues occur

Account Details:
- Role: ${employeeData.account_details?.role || "delivery_agent"}
- Status: ${employeeData.account_details?.status || "active"}
- Created: ${
      employeeData.account_details?.created_date ||
      new Date().toISOString().split("T")[0]
    }

Next Steps:
${
  employeeData.next_steps
    ?.map((step, index) => `${index + 1}. ${step}`)
    .join("\n") || ""
}
    `;

    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${employeeData.agent_id}_credentials.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const sendCredentialsEmail = () => {
    const subject = `Delivery Agent Login Credentials - ${employeeData.agent_id}`;
    const body = `Dear ${employeeData.full_name},

Welcome to our delivery team! Your account has been created successfully.

Login Details:
- Website: ${
      employeeData.login_instructions?.website || "http://localhost:5173/login"
    }
- Username: ${employeeData.username}
- Temporary Password: ${employeeData.temporary_password}

IMPORTANT:
- You must change your password on first login
- Keep your credentials secure
- Contact your supervisor if you have any login issues

Best regards,
Management Team`;

    const mailtoLink = `mailto:${
      employeeData.account_details?.email
    }?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoLink);
  };

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-lg w-full max-w-4xl max-h-[95vh] overflow-hidden flex flex-col'>
        {/* Header */}
        <div className='bg-green-600 text-white p-6 flex-shrink-0'>
          <div className='flex items-center space-x-3'>
            <CheckCircle className='h-8 w-8' />
            <div>
              <h2 className='text-2xl font-bold'>
                Employee Created Successfully!
              </h2>
              <p className='text-green-100'>Login credentials generated</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className='flex-1 overflow-y-auto p-6'>
          {/* Employee Info */}
          <Card className='mb-6 p-4 bg-blue-50 border-blue-200'>
            <h3 className='text-lg font-semibold text-blue-900 mb-3'>
              Employee Information
            </h3>
            <div className='grid grid-cols-2 gap-4 text-sm'>
              <div>
                <span className='font-medium text-gray-700'>Full Name:</span>
                <p className='text-gray-900'>{employeeData.full_name}</p>
              </div>
              <div>
                <span className='font-medium text-gray-700'>Agent ID:</span>
                <p className='text-gray-900 font-mono'>
                  {employeeData.agent_id}
                </p>
              </div>
              <div>
                <span className='font-medium text-gray-700'>Phone:</span>
                <p className='text-gray-900'>{employeeData.phone_number}</p>
              </div>
              <div>
                <span className='font-medium text-gray-700'>Hire Date:</span>
                <p className='text-gray-900'>{employeeData.hire_date}</p>
              </div>
            </div>
          </Card>

          {/* Login Credentials */}
          <Card className='mb-6 p-4 bg-yellow-50 border-yellow-200'>
            <h3 className='text-lg font-semibold text-yellow-900 mb-3'>
              Login Credentials
            </h3>

            {/* Website */}
            <div className='mb-4'>
              <label className='block text-sm font-medium text-gray-700 mb-1'>
                Login Website
              </label>
              <div className='flex items-center space-x-2'>
                <input
                  type='text'
                  value={
                    employeeData.login_instructions?.website ||
                    "http://localhost:5173/login"
                  }
                  readOnly
                  className='flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm'
                />
                <Button
                  onClick={() =>
                    copyToClipboard(
                      employeeData.login_instructions?.website ||
                        "http://localhost:5173/login",
                      "website"
                    )
                  }
                  className='px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white'
                >
                  {copiedField === "website" ? (
                    <CheckCircle className='h-4 w-4' />
                  ) : (
                    <Copy className='h-4 w-4' />
                  )}
                </Button>
              </div>
            </div>

            {/* Username */}
            <div className='mb-4'>
              <label className='block text-sm font-medium text-gray-700 mb-1'>
                Username
              </label>
              <div className='flex items-center space-x-2'>
                <div className='flex-1 flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md bg-gray-50'>
                  <User className='h-4 w-4 text-gray-500' />
                  <span className='font-mono text-lg font-bold text-blue-600'>
                    {employeeData.username}
                  </span>
                </div>
                <Button
                  onClick={() =>
                    copyToClipboard(employeeData.username, "username")
                  }
                  className='px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white'
                >
                  {copiedField === "username" ? (
                    <CheckCircle className='h-4 w-4' />
                  ) : (
                    <Copy className='h-4 w-4' />
                  )}
                </Button>
              </div>
            </div>

            {/* Password */}
            <div className='mb-4'>
              <label className='block text-sm font-medium text-gray-700 mb-1'>
                Temporary Password
              </label>
              <div className='flex items-center space-x-2'>
                <div className='flex-1 flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md bg-gray-50'>
                  <Lock className='h-4 w-4 text-gray-500' />
                  <span className='font-mono text-lg font-bold text-red-600'>
                    {showPassword
                      ? employeeData.temporary_password
                      : "••••••••"}
                  </span>
                </div>
                <Button
                  onClick={() => setShowPassword(!showPassword)}
                  className='px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white'
                >
                  {showPassword ? (
                    <EyeOff className='h-4 w-4' />
                  ) : (
                    <Eye className='h-4 w-4' />
                  )}
                </Button>
                <Button
                  onClick={() =>
                    copyToClipboard(employeeData.temporary_password, "password")
                  }
                  className='px-3 py-2 bg-red-600 hover:bg-red-700 text-white'
                >
                  {copiedField === "password" ? (
                    <CheckCircle className='h-4 w-4' />
                  ) : (
                    <Copy className='h-4 w-4' />
                  )}
                </Button>
              </div>
            </div>

            {/* Important Notice */}
            <div className='bg-orange-50 border border-orange-200 rounded-lg p-3'>
              <div className='flex items-start space-x-2'>
                <AlertCircle className='h-5 w-5 text-orange-500 flex-shrink-0 mt-0.5' />
                <div className='text-sm text-orange-800'>
                  <p className='font-medium mb-1'>Important:</p>
                  <ul className='list-disc list-inside space-y-1'>
                    <li>Employee must change password on first login</li>
                    <li>Keep these credentials secure</li>
                    <li>Provide these details to the employee immediately</li>
                  </ul>
                </div>
              </div>
            </div>
          </Card>

          {/* Next Steps */}
          <Card className='mb-6 p-4'>
            <h3 className='text-lg font-semibold text-gray-900 mb-3'>
              Next Steps
            </h3>
            <ol className='list-decimal list-inside space-y-2 text-sm text-gray-700'>
              {employeeData.next_steps?.map((step, index) => (
                <li key={index}>{step}</li>
              )) || [
                "Provide login credentials to employee",
                "Employee must change password on first login",
                "Schedule training session",
                "Collect required documents",
                "Complete background check",
                "Set up work schedule",
              ]}
            </ol>
          </Card>
        </div>

        {/* Footer */}
        <div className='bg-gray-50 px-6 py-4 border-t flex-shrink-0'>
          <div className='flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4'>
            {/* Action Buttons */}
            <div className='flex flex-col sm:flex-row gap-3'>
              <Button
                onClick={generateCredentialsPDF}
                className='bg-green-600 hover:bg-green-700 text-white flex items-center justify-center space-x-2 px-4 py-2 text-sm'
              >
                <Download className='h-4 w-4' />
                <span className='hidden sm:inline'>Download Credentials</span>
                <span className='sm:hidden'>Download</span>
              </Button>
              {employeeData.account_details?.email && (
                <Button
                  onClick={sendCredentialsEmail}
                  className='bg-blue-600 hover:bg-blue-700 text-white flex items-center justify-center space-x-2 px-4 py-2 text-sm'
                >
                  <Mail className='h-4 w-4' />
                  <span className='hidden sm:inline'>Email to Employee</span>
                  <span className='sm:hidden'>Email</span>
                </Button>
              )}
              <Button
                onClick={() =>
                  copyToClipboard(
                    `Username: ${employeeData.username}\nPassword: ${employeeData.temporary_password}`,
                    "all"
                  )
                }
                className='bg-purple-600 hover:bg-purple-700 text-white flex items-center justify-center space-x-2 px-4 py-2 text-sm'
              >
                {copiedField === "all" ? (
                  <CheckCircle className='h-4 w-4' />
                ) : (
                  <Copy className='h-4 w-4' />
                )}
                <span className='hidden sm:inline'>Copy All Credentials</span>
                <span className='sm:hidden'>Copy All</span>
              </Button>
            </div>

            {/* Close Button */}
            <div className='flex justify-end'>
              <Button
                onClick={onClose}
                className='bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 min-w-[100px] text-sm'
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeCredentialsModal;
