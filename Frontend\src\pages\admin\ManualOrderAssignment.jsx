import React, { useState, useEffect } from 'react';
import {
  Package,
  Users,
  Clock,
  MapPin,
  Phone,
  DollarSign,
  ArrowRight,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  User,
  Truck,
  Star,
  Navigation
} from 'lucide-react';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Badge from '../../components/common/Badge';
import { adminApi } from '../../services/adminApi';

const ManualOrderAssignment = () => {
  const [readyOrders, setReadyOrders] = useState([]);
  const [availableAgents, setAvailableAgents] = useState([]);
  const [activeDeliveries, setActiveDeliveries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [assignmentLoading, setAssignmentLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [assignmentNotes, setAssignmentNotes] = useState('');
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);

  useEffect(() => {
    loadAssignmentData();
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadAssignmentData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadAssignmentData = async () => {
    try {
      setLoading(true);
      const [ordersResponse, deliveriesResponse] = await Promise.all([
        adminApi.getOrderAssignmentData(),
        adminApi.getActiveDeliveries()
      ]);

      if (ordersResponse.success) {
        setReadyOrders(ordersResponse.data.ready_orders);
        setAvailableAgents(ordersResponse.data.available_agents);
      }

      if (deliveriesResponse.success) {
        setActiveDeliveries(deliveriesResponse.data.active_deliveries);
      }
    } catch (error) {
      console.error('Failed to load assignment data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignOrder = (order, agent) => {
    setSelectedOrder(order);
    setSelectedAgent(agent);
    setAssignmentNotes('');
    setShowAssignmentModal(true);
  };

  const confirmAssignment = async () => {
    if (!selectedOrder || !selectedAgent) return;

    try {
      setAssignmentLoading(true);
      const response = await adminApi.assignOrderToAgent({
        order_id: selectedOrder.id,
        agent_id: selectedAgent.agent_id,
        notes: assignmentNotes
      });

      if (response.success) {
        setShowAssignmentModal(false);
        setSelectedOrder(null);
        setSelectedAgent(null);
        setAssignmentNotes('');
        loadAssignmentData(); // Refresh data
      }
    } catch (error) {
      console.error('Assignment failed:', error);
    } finally {
      setAssignmentLoading(false);
    }
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'AFN',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      assigned: { variant: 'info', label: 'Assigned' },
      picked_up: { variant: 'warning', label: 'Picked Up' },
      on_the_way: { variant: 'primary', label: 'On the Way' },
      delivered: { variant: 'success', label: 'Delivered' }
    };
    
    const config = statusConfig[status] || { variant: 'secondary', label: status };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Manual Order Assignment</h1>
              <p className="text-gray-600 mt-1">Assign orders to delivery agents manually</p>
            </div>
            <Button
              variant="outline"
              onClick={loadAssignmentData}
              loading={loading}
              icon={<RefreshCw size={16} />}
            >
              Refresh
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-orange-100 rounded-lg">
                  <Package className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Ready Orders</p>
                  <p className="text-2xl font-bold text-gray-900">{readyOrders.length}</p>
                </div>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-lg">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Available Agents</p>
                  <p className="text-2xl font-bold text-gray-900">{availableAgents.length}</p>
                </div>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Truck className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Deliveries</p>
                  <p className="text-2xl font-bold text-gray-900">{activeDeliveries.length}</p>
                </div>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Clock className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg. Assignment Time</p>
                  <p className="text-2xl font-bold text-gray-900">2.5m</p>
                </div>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Ready Orders */}
          <Card>
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Package className="mr-2" size={20} />
                Orders Ready for Assignment ({readyOrders.length})
              </h2>
              
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">Loading orders...</p>
                </div>
              ) : readyOrders.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No orders ready for assignment</p>
                </div>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {readyOrders.map((order) => (
                    <div
                      key={order.id}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedOrder?.id === order.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedOrder(order)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-semibold text-gray-900">Order #{order.id}</h3>
                            <span className="text-lg font-bold text-green-600">
                              {formatCurrency(order.total_amount)}
                            </span>
                          </div>
                          
                          <div className="space-y-1 text-sm text-gray-600">
                            <div className="flex items-center">
                              <User className="mr-2" size={14} />
                              {order.customer_name} • {order.customer_phone}
                            </div>
                            <div className="flex items-center">
                              <MapPin className="mr-2" size={14} />
                              {order.restaurant_name}
                            </div>
                            <div className="flex items-center">
                              <Navigation className="mr-2" size={14} />
                              {order.delivery_address}
                            </div>
                            <div className="flex items-center">
                              <Clock className="mr-2" size={14} />
                              Created: {formatTime(order.created_at)}
                            </div>
                          </div>
                          
                          {order.special_instructions && (
                            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                              <strong>Instructions:</strong> {order.special_instructions}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>

          {/* Available Agents */}
          <Card>
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Users className="mr-2" size={20} />
                Available Agents ({availableAgents.length})
              </h2>
              
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">Loading agents...</p>
                </div>
              ) : availableAgents.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No agents available</p>
                </div>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {availableAgents.map((agent) => (
                    <div
                      key={agent.id}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedAgent?.id === agent.id
                          ? 'border-green-500 bg-green-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedAgent(agent)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-semibold text-gray-900">
                              {agent.full_name}
                            </h3>
                            <Badge variant="success">Available</Badge>
                          </div>
                          
                          <div className="space-y-1 text-sm text-gray-600">
                            <div className="flex items-center">
                              <User className="mr-2" size={14} />
                              {agent.agent_id} • {agent.employee_number}
                            </div>
                            <div className="flex items-center">
                              <Phone className="mr-2" size={14} />
                              {agent.phone_number}
                            </div>
                            <div className="flex items-center">
                              <Truck className="mr-2" size={14} />
                              {agent.vehicle_type}
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <Star className="mr-1" size={14} />
                                {agent.rating}/5.0
                              </div>
                              <div className="flex items-center">
                                <Package className="mr-1" size={14} />
                                {agent.total_deliveries} deliveries
                              </div>
                            </div>
                          </div>
                          
                          {agent.current_location?.address && (
                            <div className="mt-2 text-xs text-gray-500">
                              <MapPin className="inline mr-1" size={12} />
                              {agent.current_location.address}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Assignment Action */}
        {selectedOrder && selectedAgent && (
          <Card className="mt-8">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Assignment Preview</h3>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <Package className="h-8 w-8 text-blue-600 mx-auto mb-1" />
                    <p className="text-sm font-medium">Order #{selectedOrder.id}</p>
                    <p className="text-xs text-gray-500">{selectedOrder.restaurant_name}</p>
                  </div>
                  
                  <ArrowRight className="h-6 w-6 text-gray-400" />
                  
                  <div className="text-center">
                    <User className="h-8 w-8 text-green-600 mx-auto mb-1" />
                    <p className="text-sm font-medium">{selectedAgent.full_name}</p>
                    <p className="text-xs text-gray-500">{selectedAgent.agent_id}</p>
                  </div>
                </div>
                
                <Button
                  onClick={() => handleAssignOrder(selectedOrder, selectedAgent)}
                  icon={<CheckCircle size={16} />}
                >
                  Assign Order
                </Button>
              </div>
            </div>
          </Card>
        )}

        {/* Active Deliveries */}
        <Card className="mt-8">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Truck className="mr-2" size={20} />
              Active Deliveries ({activeDeliveries.length})
            </h2>
            
            {activeDeliveries.length === 0 ? (
              <div className="text-center py-8">
                <Truck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No active deliveries</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Agent
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Assigned
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {activeDeliveries.map((delivery) => (
                      <tr key={delivery.order_id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              Order #{delivery.order_id}
                            </div>
                            <div className="text-sm text-gray-500">
                              {delivery.restaurant_name}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {delivery.agent.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {delivery.agent.agent_id}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(delivery.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {delivery.customer_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {delivery.customer_phone}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatTime(delivery.assigned_at)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </Card>

        {/* Assignment Confirmation Modal */}
        {showAssignmentModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Confirm Assignment
              </h3>
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  Assign Order #{selectedOrder?.id} to {selectedAgent?.full_name}?
                </p>
                
                <div className="space-y-2 text-sm">
                  <div><strong>Restaurant:</strong> {selectedOrder?.restaurant_name}</div>
                  <div><strong>Customer:</strong> {selectedOrder?.customer_name}</div>
                  <div><strong>Agent:</strong> {selectedAgent?.full_name} ({selectedAgent?.agent_id})</div>
                  <div><strong>Amount:</strong> {formatCurrency(selectedOrder?.total_amount)}</div>
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assignment Notes (Optional)
                </label>
                <textarea
                  value={assignmentNotes}
                  onChange={(e) => setAssignmentNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Any special instructions for the agent..."
                />
              </div>
              
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setShowAssignmentModal(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={confirmAssignment}
                  loading={assignmentLoading}
                  className="flex-1"
                >
                  Confirm Assignment
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManualOrderAssignment;
