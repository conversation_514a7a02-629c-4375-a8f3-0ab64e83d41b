import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import {
  User,
  Mail,
  Phone,
  Store,
  Clock,
  AlertCircle,
  Loader,
} from "lucide-react";
import { cuisineCategories } from "../../data/restaurants";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Input from "../../components/common/Input";

function RestaurantProfile() {
  const { user } = useAuth();
  const {
    currentRestaurant,
    getUserRestaurants,
    updateRestaurant,
    patchRestaurant,
    loading: restaurantLoading,
    error: restaurantError,
  } = useRestaurant();

  const [activeTab, setActiveTab] = useState("restaurant");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [initialLoading, setInitialLoading] = useState(true);

  // Restaurant data state
  const [restaurant, setRestaurant] = useState({
    name: "",
    description: "",
    cuisine: "",
    cuisineId: null, // Store the cuisine ID for API calls
    phone: "",
    email: "",
    address: "",
    openingTime: "09:00:00",
    closingTime: "22:00:00",
    minimumOrder: 15.0,
  });

  // Available cuisine types from backend
  const [cuisineTypes, setCuisineTypes] = useState([]);

  // Load cuisine types from backend
  const loadCuisineTypes = async () => {
    try {
      const response = await fetch('http://127.0.0.1:8000/api/restaurant/cuisine-types/');

      if (response.ok) {
        const data = await response.json();
        setCuisineTypes(data);
      } else {
        console.error("Failed to load cuisine types:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Error loading cuisine types:", error);
    }
  };

  // Load restaurant data on component mount
  useEffect(() => {
    const loadRestaurantData = async () => {
      if (user && user.role === "restaurant") {
        try {
          setInitialLoading(true);

          // Load cuisine types first
          await loadCuisineTypes();

          const result = await getUserRestaurants();

          if (result.success && result.data.length > 0) {
            const userRestaurant = result.data[0]; // Get first restaurant
            const currentCuisine = userRestaurant.cuisine_types?.[0];

            setRestaurant({
              id: userRestaurant.id,
              name: userRestaurant.name || "",
              description: userRestaurant.description || "",
              cuisine: currentCuisine?.name || "",
              cuisineId: currentCuisine?.id || null,
              phone: userRestaurant.contact_number || "",
              email: user?.email || "", // Get email from user context (restaurant owner)
              address: userRestaurant.address_detail?.street || "",
              openingTime: userRestaurant.opening_time || "09:00:00",
              closingTime: userRestaurant.closing_time || "22:00:00",
              minimumOrder: userRestaurant.minimum_order_amount || 15.0,
            });
          }
        } catch (error) {
          console.error("Error loading restaurant data:", error);
          setError("Failed to load restaurant data");
        } finally {
          setInitialLoading(false);
        }
      } else {
        setInitialLoading(false);
      }
    };

    loadRestaurantData();
  }, [user, getUserRestaurants]);

  const handleSave = async () => {
    if (!restaurant.id) {
      setError("No restaurant found to update");
      return;
    }

    if (cuisineTypes.length === 0) {
      setError("Cuisine types are not loaded. Please wait and try again.");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Prepare update data (email is not part of restaurant model)
      const updateData = {
        name: restaurant.name,
        description: restaurant.description,
        contact_number: restaurant.phone,
        opening_time: restaurant.openingTime.includes(':') && restaurant.openingTime.split(':').length === 2
          ? `${restaurant.openingTime}:00`
          : restaurant.openingTime,
        closing_time: restaurant.closingTime.includes(':') && restaurant.closingTime.split(':').length === 2
          ? `${restaurant.closingTime}:00`
          : restaurant.closingTime,
        minimum_order_amount: parseFloat(restaurant.minimumOrder),
        address: {
          street: restaurant.address,
          city: "Kabul", // Default for now
          state: "Kabul",
          country: "Afghanistan",
          postal_code: "1001",
        },
      };

      // Add cuisine type IDs if selected
      if (restaurant.cuisineId) {
        updateData.cuisine_type_ids = [restaurant.cuisineId];
      }





      // Use patch for partial update
      const result = await patchRestaurant(restaurant.id, updateData);

      if (result.success) {
        setSuccess(true);
        console.log("Restaurant updated successfully:", result.data);

        // Update local state with response data
        const updatedRestaurant = result.data;
        const updatedCuisine = updatedRestaurant.cuisine_types?.[0];



        // Force update the state with the latest data
        setRestaurant({
          id: updatedRestaurant.id,
          name: updatedRestaurant.name || restaurant.name,
          description: updatedRestaurant.description || restaurant.description,
          cuisine: updatedCuisine?.name || "",
          cuisineId: updatedCuisine?.id || null,
          phone: updatedRestaurant.contact_number || restaurant.phone,
          email: user?.email || restaurant.email,
          address: updatedRestaurant.address_detail?.street || restaurant.address,
          openingTime: updatedRestaurant.opening_time || restaurant.openingTime,
          closingTime: updatedRestaurant.closing_time || restaurant.closingTime,
          minimumOrder: updatedRestaurant.minimum_order_amount || restaurant.minimumOrder,
        });


        // Refresh the restaurant data to get latest from server
        setTimeout(async () => {
          try {
            const refreshResult = await getUserRestaurants();
            if (refreshResult.success && refreshResult.data.length > 0) {
              const updatedRestaurant = refreshResult.data[0];
              const refreshedCuisine = updatedRestaurant.cuisine_types?.[0];

              setRestaurant({
                id: updatedRestaurant.id,
                name: updatedRestaurant.name || "",
                description: updatedRestaurant.description || "",
                cuisine: refreshedCuisine?.name || "",
                cuisineId: refreshedCuisine?.id || null,
                phone: updatedRestaurant.contact_number || "",
                email: user?.email || "", // Keep using user's email
                address: updatedRestaurant.address_detail?.street || "",
                openingTime: updatedRestaurant.opening_time || "09:00:00",
                closingTime: updatedRestaurant.closing_time || "22:00:00",
                minimumOrder: updatedRestaurant.minimum_order_amount || 15.0,
              });
              console.log("Restaurant data refreshed after update");
            }
          } catch (error) {
            console.error("Error refreshing restaurant data:", error);
          }
        }, 500);

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(false), 3000);
      } else {
        setError(result.error || "Failed to update restaurant");
      }
    } catch (error) {
      console.error("Error saving restaurant:", error);
      setError("Failed to update restaurant");
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: "restaurant", label: "Restaurant Info", icon: Store },
    { id: "contact", label: "Contact", icon: Phone },
    { id: "hours", label: "Hours", icon: Clock },
    { id: "account", label: "Account", icon: User },
  ];

  // Show loading state while fetching initial data
  if (initialLoading) {
    return (
      <div className="mx-auto px-4 py-8 container">
        <div className="mx-auto max-w-4xl">
          <div className="flex justify-center items-center py-12">
            <Loader className="mr-3 w-8 h-8 text-primary-500 animate-spin" />
            <span className="text-gray-600 text-lg">Loading restaurant profile...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto px-4 py-8 container">
      <div className="mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="mb-2 font-bold text-gray-900 text-3xl">Restaurant Profile</h1>
          <p className="text-gray-600">Manage your restaurant information and settings</p>
        </div>

        {/* Success Message */}
        {success && (
          <div className="bg-green-50 mb-6 p-4 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="mr-2 w-5 h-5 text-green-500" />
              <span className="text-green-800">Profile updated successfully!</span>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 mb-6 p-4 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="mr-2 w-5 h-5 text-red-500" />
              <span className="text-red-800">{error}</span>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-gray-200 border-b">
            <nav className="flex space-x-8 -mb-px">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? "border-primary-500 text-primary-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    }`}
                  >
                    <Icon size={16} />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <Card className="p-6">
          {activeTab === "restaurant" && (
            <div className="space-y-6">
              <h2 className="mb-4 font-semibold text-xl">Restaurant Information</h2>
              
              <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
                <div>
                  <label className="block mb-2 font-medium text-gray-700 text-sm">
                    Restaurant Name
                  </label>
                  <Input
                    value={restaurant.name}
                    onChange={(e) => setRestaurant({...restaurant, name: e.target.value})}
                    placeholder="Enter restaurant name"
                  />
                </div>
                
                <div>
                  <label className="block mb-2 font-medium text-gray-700 text-sm">
                    Cuisine Type
                  </label>
                  <select
                    value={restaurant.cuisineId || ""}
                    onChange={(e) => {
                      const selectedId = parseInt(e.target.value);
                      const selectedCuisine = cuisineTypes.find(c => c.id === selectedId);
                      setRestaurant({
                        ...restaurant,
                        cuisineId: selectedId,
                        cuisine: selectedCuisine?.name || ""
                      });
                    }}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 w-full"
                  >
                    <option value="">Select Cuisine Type</option>
                    {cuisineTypes.length > 0 ? (
                      cuisineTypes.map((cuisine) => (
                        <option key={cuisine.id} value={cuisine.id}>
                          {cuisine.name}
                        </option>
                      ))
                    ) : (
                      <option disabled>Loading cuisine types...</option>
                    )}
                  </select>
                </div>
              </div>

              <div>
                <label className="block mb-2 font-medium text-gray-700 text-sm">
                  Description
                </label>
                <textarea
                  value={restaurant.description}
                  onChange={(e) => setRestaurant({...restaurant, description: e.target.value})}
                  rows={4}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 w-full"
                  placeholder="Describe your restaurant..."
                />
              </div>
            </div>
          )}

          {activeTab === "contact" && (
            <div className="space-y-6">
              <h2 className="mb-4 font-semibold text-xl">Contact Information</h2>
              
              <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
                <div>
                  <label className="block mb-2 font-medium text-gray-700 text-sm">
                    Phone Number
                  </label>
                  <Input
                    value={restaurant.phone}
                    onChange={(e) => setRestaurant({...restaurant, phone: e.target.value})}
                    placeholder="Enter phone number"
                  />
                </div>
                
                <div>
                  <label className="block mb-2 font-medium text-gray-700 text-sm">
                    Email Address
                  </label>
                  <Input
                    value={restaurant.email}
                    readOnly
                    className="bg-gray-50 cursor-not-allowed"
                    placeholder="Email from user account"
                  />
                  <p className="mt-1 text-gray-500 text-xs">
                    Email is linked to your user account. To change it, update your account settings.
                  </p>
                </div>
              </div>

              <div>
                <label className="block mb-2 font-medium text-gray-700 text-sm">
                  Address
                </label>
                <Input
                  value={restaurant.address}
                  onChange={(e) => setRestaurant({...restaurant, address: e.target.value})}
                  placeholder="Enter full address"
                />
              </div>
            </div>
          )}

          {activeTab === "hours" && (
            <div className="space-y-6">
              <h2 className="mb-4 font-semibold text-xl">Operating Hours</h2>
              
              <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
                <div>
                  <label className="block mb-2 font-medium text-gray-700 text-sm">
                    Opening Time
                  </label>
                  <Input
                    type="time"
                    value={restaurant.openingTime.substring(0, 5)} // Show only HH:MM in input
                    onChange={(e) => {
                      const timeValue = e.target.value; // HH:MM format
                      setRestaurant({
                        ...restaurant,
                        openingTime: timeValue.includes(':') ? `${timeValue}:00` : timeValue
                      });
                    }}
                  />
                </div>

                <div>
                  <label className="block mb-2 font-medium text-gray-700 text-sm">
                    Closing Time
                  </label>
                  <Input
                    type="time"
                    value={restaurant.closingTime.substring(0, 5)} // Show only HH:MM in input
                    onChange={(e) => {
                      const timeValue = e.target.value; // HH:MM format
                      setRestaurant({
                        ...restaurant,
                        closingTime: timeValue.includes(':') ? `${timeValue}:00` : timeValue
                      });
                    }}
                  />
                </div>
              </div>

              <div>
                <label className="block mb-2 font-medium text-gray-700 text-sm">
                  Minimum Order Amount ($)
                </label>
                <Input
                  type="number"
                  step="0.01"
                  value={restaurant.minimumOrder}
                  onChange={(e) => setRestaurant({...restaurant, minimumOrder: parseFloat(e.target.value)})}
                  placeholder="Enter minimum order amount"
                />
              </div>
            </div>
          )}

          {activeTab === "account" && (
            <div className="space-y-6">
              <h2 className="mb-4 font-semibold text-xl">Account Settings</h2>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="mb-2 font-medium text-gray-900">Account Information</h3>
                <p className="mb-1 text-gray-600">Name: {user?.name}</p>
                <p className="mb-1 text-gray-600">Username: {user?.user_name}</p>
                <p className="mb-1 text-gray-600">Email: {user?.email}</p>
                <p className="text-gray-600">Role: {user?.role}</p>
              </div>
            </div>
          )}

          {/* Save Button */}
          <div className="mt-8 pt-6 border-gray-200 border-t">
            <Button
              onClick={handleSave}
              disabled={loading}
              className="w-full md:w-auto"
            >
              {loading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}

export default RestaurantProfile;
