import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { cartApi } from "../utils/orderApi";
import { useAuth } from "./AuthContext";

const CartContext = createContext(null);

export const useCart = () => useContext(CartContext);

export const CartProvider = ({ children }) => {
  const { user } = useAuth();
  const [cart, setCart] = useState({
    restaurantId: null,
    restaurantName: "",
    items: [],
    subtotal: 0,
    deliveryFee: 0,
    total: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Load cart from localStorage on component mount
    const savedCart = localStorage.getItem("afghanSofraCart");
    if (savedCart) {
      setCart(JSON.parse(savedCart));
    }
  }, []);

  useEffect(() => {
    // Save cart to localStorage whenever it changes
    localStorage.setItem("afghanSofraCart", JSON.stringify(cart));
  }, [cart]);

  const addToCart = (restaurantId, restaurantName, item, quantity = 1) => {
    // Check if trying to add item from different restaurant
    if (cart.restaurantId && cart.restaurantId !== restaurantId) {
      return {
        success: false,
        error: "Items in cart are from another restaurant. Clear cart first.",
      };
    }

    setCart((prevCart) => {
      // Check if item already exists in cart
      const existingItemIndex = prevCart.items.findIndex(
        (cartItem) => cartItem.id === item.id
      );

      let newItems = [...prevCart.items];

      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        newItems[existingItemIndex] = {
          ...newItems[existingItemIndex],
          quantity: newItems[existingItemIndex].quantity + quantity,
        };
      } else {
        // Add new item
        newItems.push({
          ...item,
          quantity,
        });
      }

      // Calculate new totals
      const subtotal = newItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );

      // For this demo, we'll use a fixed delivery fee or calculate based on subtotal
      const deliveryFee = restaurantId ? 5 : 0; // Example fixed fee

      return {
        restaurantId,
        restaurantName,
        items: newItems,
        subtotal,
        deliveryFee,
        total: subtotal + deliveryFee,
      };
    });

    return { success: true };
  };

  const updateItemQuantity = (itemId, quantity) => {
    if (quantity <= 0) {
      return removeFromCart(itemId);
    }

    setCart((prevCart) => {
      const newItems = prevCart.items.map((item) =>
        item.id === itemId ? { ...item, quantity } : item
      );

      const subtotal = newItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );

      return {
        ...prevCart,
        items: newItems,
        subtotal,
        total: subtotal + prevCart.deliveryFee,
      };
    });
  };

  const removeFromCart = (itemId) => {
    setCart((prevCart) => {
      const newItems = prevCart.items.filter((item) => item.id !== itemId);

      const subtotal = newItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );

      // If cart becomes empty, reset restaurant info
      const newRestaurantId =
        newItems.length > 0 ? prevCart.restaurantId : null;
      const newRestaurantName =
        newItems.length > 0 ? prevCart.restaurantName : "";
      const deliveryFee = newItems.length > 0 ? prevCart.deliveryFee : 0;

      return {
        restaurantId: newRestaurantId,
        restaurantName: newRestaurantName,
        items: newItems,
        subtotal,
        deliveryFee,
        total: subtotal + deliveryFee,
      };
    });
  };

  const clearCart = useCallback(() => {
    setCart({
      restaurantId: null,
      restaurantName: "",
      items: [],
      subtotal: 0,
      deliveryFee: 0,
      total: 0,
    });
  }, []);

  /**
   * Load cart from API
   */
  const loadCartFromAPI = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const result = await cartApi.getCart();

      if (result.success && result.data) {
        const apiCart = result.data;

        // Transform API cart to local cart format
        const transformedCart = {
          restaurantId: apiCart.restaurant?.id || null,
          restaurantName: apiCart.restaurant?.name || "",
          items:
            apiCart.items?.map((item) => ({
              id: item.menu_item.id,
              name: item.menu_item.name,
              price: parseFloat(item.menu_item.price),
              image: item.menu_item.image,
              description: item.menu_item.description,
              is_vegetarian: item.menu_item.is_vegetarian,
              is_available: item.menu_item.is_available,
              preparation_time: item.menu_item.preparation_time,
              quantity: item.quantity,
              special_requests: item.special_requests || "",
            })) || [],
          subtotal: 0,
          deliveryFee: parseFloat(apiCart.restaurant?.delivery_fee || 0),
          total: 0,
        };

        // Calculate totals
        const subtotal = transformedCart.items.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0
        );
        transformedCart.subtotal = subtotal;
        transformedCart.total = subtotal + transformedCart.deliveryFee;

        setCart(transformedCart);
      }
    } catch (err) {
      console.error("Load cart from API error:", err);
      setError("Failed to load cart");
    } finally {
      setLoading(false);
    }
  }, [user]);

  /**
   * Save cart to API
   */
  const saveCartToAPI = useCallback(async () => {
    if (!user || !cart.restaurantId || cart.items.length === 0) return;

    setLoading(true);
    setError(null);

    try {
      const cartData = {
        restaurant_id: cart.restaurantId,
        items: cart.items.map((item) => ({
          menu_item_id: item.id,
          quantity: item.quantity,
          special_requests: item.special_requests || "",
        })),
      };

      const result = await cartApi.saveCart(cartData);

      if (!result.success) {
        setError(result.error);
        console.error("Failed to save cart:", result.error);
      }
    } catch (err) {
      console.error("Save cart to API error:", err);
      setError("Failed to save cart");
    } finally {
      setLoading(false);
    }
  }, [user, cart.restaurantId, cart.items]);

  /**
   * Delete cart from API
   */
  const deleteCartFromAPI = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const result = await cartApi.deleteCart();

      if (result.success) {
        clearCart();
      } else {
        setError(result.error);
        console.error("Failed to delete cart:", result.error);
      }
    } catch (err) {
      console.error("Delete cart from API error:", err);
      setError("Failed to delete cart");
    } finally {
      setLoading(false);
    }
  };

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Load cart from API when user logs in
  useEffect(() => {
    if (user) {
      loadCartFromAPI();
    }
  }, [user]);

  // Auto-save cart to API when cart changes (debounced)
  useEffect(() => {
    if (user && cart.items.length > 0) {
      const timeoutId = setTimeout(() => {
        saveCartToAPI();
      }, 1000); // Save after 1 second of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [cart, user, saveCartToAPI]);

  const value = {
    cart,
    loading,
    error,
    addToCart,
    updateItemQuantity,
    removeFromCart,
    clearCart,
    loadCartFromAPI,
    saveCartToAPI,
    deleteCartFromAPI,
    clearError,
    itemCount: cart.items.reduce((count, item) => count + item.quantity, 0),
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};
