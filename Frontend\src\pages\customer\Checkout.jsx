import React, { useState, useEffect } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { useForm } from "react-hook-form";
import { useCart } from "../../context/CartContext";
import { useAuth } from "../../context/AuthContext";
import { useOrder } from "../../context/OrderContext";
import { useLoyalty } from "../../context/LoyaltyContext";
import { useScheduling } from "../../context/SchedulingContext";
import {
  ArrowLeft,
  MapPin,
  CreditCard,
  AlertCircle,
  Wallet,
  DollarSign,
  Calendar,
  Clock,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Input from "../../components/common/Input";
import FormControl from "../../components/common/FormControl";
import AddressManager from "../../components/map/AddressManager";
import { useLocation } from "../../hooks/useLocation";
import { useDeliveryFee } from "../../hooks/useDeliveryFee";
import DeliveryFeeDisplay from "../../components/delivery/DeliveryFeeDisplay";
import ScheduleOrderModal from "../../components/scheduling/ScheduleOrderModal";

const Checkout = () => {
  const { cart, clearCart } = useCart();
  const { user } = useAuth();
  const { createOrder, loading: orderLoading, error: orderError } = useOrder();
  const { processOrderCompletion, loyaltyData, LOYALTY_TIERS } = useLoyalty();
  const { scheduleOrder, isScheduled, setIsScheduled, selectedDateTime } =
    useScheduling();
  const navigate = useNavigate();

  // Payment method state
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState("cash_on_delivery");

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: user?.name || "",
      phone: user?.phone || "",
      address: user?.address || "",
      notes: "",
    },
  });

  const [loading, setLoading] = useState(false);
  const [processingStep, setProcessingStep] = useState(0);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [error, setError] = useState(null);

  // Clear error when component mounts or cart changes
  useEffect(() => {
    setError(null);
  }, [cart]);

  // Location management
  const { selectedAddress, selectAddress, hasSelectedAddress } = useLocation();

  // Helper: Get valid saved address ID
  const getValidSavedAddressId = () => {
    // First check if there's a selected address
    if (selectedAddress) {
      if (selectedAddress.type === "saved") {
        // Use backend ID if available, otherwise use the regular ID
        return selectedAddress.backendId || selectedAddress.id;
      }
      // Also accept temporary addresses for now (less restrictive)
      if (selectedAddress.id || selectedAddress.backendId) {
        return selectedAddress.backendId || selectedAddress.id;
      }
    }

    // Try to find any address if none selected
    if (Array.isArray(cart.addresses) && cart.addresses.length > 0) {
      const saved = cart.addresses.find((addr) => addr.type === "saved");
      if (saved) {
        return saved.backendId || saved.id;
      }
      // If no saved address, use any address (less restrictive)
      const anyAddress = cart.addresses[0];
      return anyAddress ? anyAddress.backendId || anyAddress.id : null;
    }

    return null;
  };

  // Dynamic delivery fee calculation
  const {
    deliveryInfo,
    deliveryFee,
    loading: deliveryLoading,
  } = useDeliveryFee(cart.restaurantId, {
    orderAmount: cart.total,
    autoCalculate: hasSelectedAddress,
  });
  const processingSteps = [
    "Validating order information...",
    "Processing your order...",
    "Sending order to restaurant...",
    "Finalizing your order...",
  ];

  // Redirect to cart if empty - use useEffect to avoid setState during render
  // But don't redirect if we're currently processing an order
  useEffect(() => {
    if (cart.items.length === 0 && !loading) {
      navigate("/cart");
    }
  }, [cart.items.length, navigate, loading]);

  // Don't render if cart is empty (unless we're processing an order)
  if (cart.items.length === 0 && !loading) {
    return null;
  }

  const onSubmit = async (data) => {
    setLoading(true);

    try {
      // Show processing steps with delays
      const stepInterval = setInterval(() => {
        setProcessingStep((prev) => {
          if (prev < processingSteps.length - 1) {
            return prev + 1;
          } else {
            clearInterval(stepInterval);
            return prev;
          }
        });
      }, 700);

      // Get delivery address ID from form data or selected address
      let deliveryAddressId = data.addressId || getValidSavedAddressId();

      // Validate address selection
      if (!deliveryAddressId) {
        setError(
          "Please select a delivery address before placing your order. You can add a new address or select from your saved addresses."
        );
        setLoading(false);
        clearInterval(stepInterval);
        return;
      }

      // Convert to integer to ensure proper type
      if (deliveryAddressId) {
        deliveryAddressId = parseInt(deliveryAddressId, 10);
      }

      // Final validation of address ID
      if (!deliveryAddressId || isNaN(deliveryAddressId)) {
        console.error("❌ Invalid delivery address ID:", deliveryAddressId);
        setError(
          "Invalid delivery address selected. Please choose a valid address from your saved addresses."
        );
        setLoading(false);
        clearInterval(stepInterval);
        return;
      }

      // Validate that the address belongs to saved addresses
      if (!selectedAddress || selectedAddress.type !== "saved") {
        setError(
          "Please select a saved delivery address. Temporary addresses cannot be used for orders."
        );
        setLoading(false);
        clearInterval(stepInterval);
        return;
      }

      // Debug cart structure
      console.log("🔍 Full cart object:", cart);
      console.log("🔍 Cart restaurant:", cart.restaurant);
      console.log("🔍 Cart restaurant_id:", cart.restaurant_id);
      console.log("🔍 Cart restaurantId:", cart.restaurantId);

      // Get restaurant ID from cart
      let restaurantId = cart.restaurantId;

      // Convert to integer to ensure proper type
      if (restaurantId) {
        restaurantId = parseInt(restaurantId, 10);
      }

      console.log("🔍 Cart restaurant ID:", restaurantId);
      console.log("🔍 Cart items count:", cart.items?.length || 0);

      // Validate restaurant ID
      if (!restaurantId || isNaN(restaurantId)) {
        console.error(
          "❌ No restaurant ID found in cart or invalid restaurant ID!"
        );
        setError(
          "Unable to determine restaurant. Please add items to your cart first."
        );
        setLoading(false);
        return;
      }

      // Prepare order data for API
      const orderData = {
        delivery_address: deliveryAddressId,
        restaurant: restaurantId,
        payment_method: selectedPaymentMethod || "cash_on_delivery",
        special_instructions: data.notes || "",
        items: cart.items.map((item) => ({
          menu_item_id: item.id,
          quantity: item.quantity,
          special_requests: item.special_requests || "",
        })),
      };

      // Debug logging
      console.log("🔍 Order Data being sent:", orderData);
      console.log("🔍 Selected address ID:", deliveryAddressId);

      // Create order via API
      const result = await createOrder(orderData);

      if (result.success) {
        console.log("🎉 Order created successfully:", result.data);
        console.log("🔍 Order ID:", result.data.id);

        // Award loyalty points for the order
        if (processOrderCompletion) {
          const pointsResult = processOrderCompletion(
            cart.total,
            result.data.id
          );
          if (pointsResult?.success) {
            console.log(`Awarded loyalty points for order ${result.data.id}`);
          }
        }

        // Stop loading and clear interval first
        clearInterval(stepInterval);
        setLoading(false);

        // Navigate to order confirmation
        console.log(
          "🚀 Navigating to order confirmation:",
          `/order-confirmation/${result.data.id}`
        );
        navigate(`/order-confirmation/${result.data.id}`);

        // Note: Cart will be cleared by the OrderConfirmation component
        // after it successfully loads the order details
      } else {
        // Handle error
        console.error("Order creation failed:", result.error);
        clearInterval(stepInterval);
        setLoading(false);
        setError(result.error || "Failed to create order. Please try again.");
      }
    } catch (error) {
      console.error("Order submission error:", error);
      console.error("🔍 Full error object:", error);
      console.error("🔍 Error response:", error.response?.data);
      console.error("🔍 Error status:", error.response?.status);

      clearInterval(stepInterval);
      setLoading(false);

      // Show specific error message if available
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        "An error occurred while placing your order. Please try again.";

      setError(errorMessage);
    }
  };

  const handleScheduleOrder = (schedulingData) => {
    setIsScheduled(true);
    setShowScheduleModal(false);
  };

  const handleOrderNow = () => {
    setIsScheduled(false);
  };

  return (
    <div className='container mx-auto px-4 py-8 max-w-4xl animate-fade-in relative'>
      {/* Processing Overlay */}
      {loading && (
        <div className='fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center'>
          <div className='bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center'>
            <div className='animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-primary-500 mx-auto mb-4'></div>
            <h3 className='text-xl font-semibold mb-2'>
              Processing Your Order
            </h3>
            <p className='text-gray-600 mb-4'>
              {processingSteps[processingStep]}
            </p>
            <div className='w-full bg-gray-200 rounded-full h-2.5'>
              <div
                className='bg-primary-500 h-2.5 rounded-full transition-all duration-500'
                style={{
                  width: `${
                    ((processingStep + 1) / processingSteps.length) * 100
                  }%`,
                }}
              ></div>
            </div>
            <p className='text-sm text-gray-500 mt-4'>
              Please don't close this page
            </p>
          </div>
        </div>
      )}

      <div className='flex items-center mb-8'>
        <Link
          to='/cart'
          className='text-text-primary hover:text-primary-500 mr-3'
        >
          <ArrowLeft size={20} />
        </Link>
        <h1 className='text-2xl font-poppins font-semibold'>Checkout</h1>
      </div>

      {/* Error Display */}
      {error && (
        <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
          <div className='flex items-center'>
            <AlertCircle className='h-5 w-5 text-red-500 mr-2' />
            <p className='text-red-700'>{error}</p>
          </div>
        </div>
      )}

      <div className='flex flex-col lg:flex-row gap-8'>
        {/* Checkout Form */}
        <div className='lg:w-2/3'>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Card className='mb-6'>
              <h2 className='text-lg font-poppins font-semibold mb-4 flex items-center'>
                <MapPin size={20} className='mr-2 text-primary-500' />
                Delivery Information
              </h2>

              <div className='space-y-4'>
                <FormControl
                  label='Full Name'
                  htmlFor='name'
                  error={errors.name?.message}
                  required
                >
                  <Input
                    id='name'
                    placeholder='Enter your full name'
                    error={errors.name?.message}
                    {...register("name", { required: "Full name is required" })}
                  />
                </FormControl>

                <FormControl
                  label='Phone Number'
                  htmlFor='phone'
                  error={errors.phone?.message}
                  required
                >
                  <Input
                    id='phone'
                    placeholder='Enter your phone number'
                    error={errors.phone?.message}
                    {...register("phone", {
                      required: "Phone number is required",
                      pattern: {
                        value: /^[0-9+\-\s()]*$/,
                        message: "Please enter a valid phone number",
                      },
                    })}
                  />
                </FormControl>

                {/* Address Selection */}
                <div className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <label className='block text-sm font-medium text-gray-700'>
                      Delivery Address <span className='text-red-500'>*</span>
                    </label>
                    {selectedAddress && (
                      <div className='flex items-center text-xs text-green-600'>
                        <MapPin size={12} className='mr-1' />
                        <span>Address Selected</span>
                      </div>
                    )}
                  </div>

                  <AddressManager
                    onAddressSelect={(address) => {
                      selectAddress(address);
                      // Update form values for both address text and ID
                      setValue("address", address.address);
                      // Use backend ID for form submission
                      setValue("addressId", address.backendId || address.id);
                    }}
                    selectedAddressId={selectedAddress?.id}
                    showAddButton={true}
                  />

                  {/* Hidden inputs for form validation */}
                  <input
                    type='hidden'
                    {...register("address", {
                      required: "Please select a delivery address",
                      validate: (value) => {
                        if (!selectedAddress) {
                          return "Please select a saved delivery address. Temporary addresses cannot be used for orders.";
                        }
                        if (selectedAddress.type !== "saved") {
                          return "Please select a saved delivery address. Temporary addresses cannot be used for orders.";
                        }
                        return true;
                      },
                    })}
                    value={selectedAddress?.address || ""}
                  />
                  <input
                    type='hidden'
                    {...register("addressId", {
                      required: "Address ID is required",
                      validate: (value) => {
                        if (!value) {
                          return "Please select an address";
                        }
                        return true;
                      },
                    })}
                    value={
                      selectedAddress?.backendId || selectedAddress?.id || ""
                    }
                  />

                  {(errors.address || errors.addressId) && (
                    <div className='mt-2 p-3 bg-red-50 border border-red-200 rounded-lg'>
                      <div className='flex items-center'>
                        <MapPin size={16} className='text-red-500 mr-2' />
                        <p className='text-sm text-red-700 font-medium'>
                          {errors.address?.message || errors.addressId?.message}
                        </p>
                      </div>
                      <p className='text-xs text-red-600 mt-1 ml-6'>
                        Please select a delivery address from the list above or
                        add a new one.
                      </p>
                    </div>
                  )}
                </div>

                {/* Delivery Fee Information */}
                {hasSelectedAddress && (
                  <DeliveryFeeDisplay
                    deliveryInfo={deliveryInfo}
                    loading={deliveryLoading}
                    variant='detailed'
                    showBreakdown={true}
                  />
                )}

                <FormControl
                  label='Additional Notes (Optional)'
                  htmlFor='notes'
                  error={errors.notes?.message}
                >
                  <textarea
                    id='notes'
                    placeholder='Delivery instructions, apartment number, etc.'
                    className='w-full px-4 py-2 bg-white border border-gray-300 rounded-md outline-none transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    rows={2}
                    {...register("notes")}
                  ></textarea>
                </FormControl>
              </div>
            </Card>

            <Card className='mb-6'>
              <h2 className='text-lg font-poppins font-semibold mb-4 flex items-center'>
                <CreditCard size={20} className='mr-2 text-primary-500' />
                Payment Method
              </h2>

              <div className='border border-gray-200 rounded-md p-4 bg-gray-50'>
                <label className='flex items-center mb-2'>
                  <input
                    type='radio'
                    name='paymentMethod'
                    value='cash_on_delivery'
                    className='w-4 h-4 text-primary-500 border-gray-300 focus:ring-primary-500'
                    checked={selectedPaymentMethod === "cash_on_delivery"}
                    onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                  />
                  <span className='ml-2 font-medium'>Cash on Delivery</span>
                </label>
                <p className='text-sm text-text-secondary pl-6'>
                  Pay with cash when your order arrives
                </p>
              </div>

              <div className='mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md'>
                <div className='flex'>
                  <AlertCircle
                    size={20}
                    className='text-yellow-600 mr-2 flex-shrink-0'
                  />
                  <div className='text-sm text-yellow-800'>
                    <p>
                      Please have the exact amount ready when your order
                      arrives.
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Order Timing */}
            <Card className='mb-6'>
              <h2 className='text-lg font-poppins font-semibold mb-4 flex items-center'>
                <Clock size={20} className='mr-2 text-primary-500' />
                Order Timing
              </h2>

              <div className='space-y-3'>
                <div
                  className={`border rounded-md p-4 cursor-pointer transition-colors ${
                    !isScheduled
                      ? "border-primary-500 bg-primary-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={handleOrderNow}
                >
                  <label className='flex items-center cursor-pointer'>
                    <input
                      type='radio'
                      name='orderTiming'
                      checked={!isScheduled}
                      onChange={handleOrderNow}
                      className='w-4 h-4 text-primary-500 border-gray-300 focus:ring-primary-500'
                    />
                    <Clock size={20} className='ml-3 mr-2 text-green-600' />
                    <div>
                      <span className='font-medium'>Order Now</span>
                      <p className='text-sm text-text-secondary'>
                        Deliver as soon as possible (30-45 mins)
                      </p>
                    </div>
                  </label>
                </div>

                <div
                  className={`border rounded-md p-4 cursor-pointer transition-colors ${
                    isScheduled
                      ? "border-primary-500 bg-primary-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={() => setShowScheduleModal(true)}
                >
                  <label className='flex items-center cursor-pointer'>
                    <input
                      type='radio'
                      name='orderTiming'
                      checked={isScheduled}
                      onChange={() => setShowScheduleModal(true)}
                      className='w-4 h-4 text-primary-500 border-gray-300 focus:ring-primary-500'
                    />
                    <Calendar size={20} className='ml-3 mr-2 text-blue-600' />
                    <div>
                      <span className='font-medium'>Schedule for Later</span>
                      <p className='text-sm text-text-secondary'>
                        Choose a specific date and time
                      </p>
                    </div>
                  </label>
                </div>

                {isScheduled && selectedDateTime && (
                  <div className='mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <p className='font-medium text-blue-800'>
                          Scheduled for:
                        </p>
                        <p className='text-blue-700 text-sm'>
                          {selectedDateTime.toLocaleDateString("en-US", {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}{" "}
                          at{" "}
                          {selectedDateTime.toLocaleTimeString("en-US", {
                            hour: "numeric",
                            minute: "2-digit",
                            hour12: true,
                          })}
                        </p>
                      </div>
                      <Button
                        variant='outline'
                        size='small'
                        onClick={() => setShowScheduleModal(true)}
                      >
                        Change
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <div className='hidden lg:block'>
              <Button
                type='submit'
                variant='primary'
                size='large'
                fullWidth
                loading={loading}
                disabled={!getValidSavedAddressId() || loading}
              >
                Place Order
              </Button>
            </div>
          </form>
        </div>

        {/* Order Summary */}
        <div className='lg:w-1/3'>
          <Card>
            <h3 className='font-poppins font-semibold text-lg mb-4'>
              Order Summary
            </h3>

            <div className='text-sm mb-4'>
              <div className='font-medium mb-2'>From {cart.restaurantName}</div>

              <div className='space-y-3'>
                {cart.items.map((item) => (
                  <div key={item.id} className='flex justify-between'>
                    <span>
                      {item.quantity}x {item.name}
                    </span>
                    <span>${(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className='border-t border-gray-100 pt-4 mt-4 space-y-3 text-sm'>
              <div className='flex justify-between'>
                <span className='text-text-secondary'>Subtotal</span>
                <span>${cart.subtotal.toFixed(2)}</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-text-secondary'>Delivery Fee</span>
                <span>
                  {deliveryLoading ? (
                    <div className='animate-pulse bg-gray-200 h-4 w-12 rounded'></div>
                  ) : deliveryFee === 0 ? (
                    <span className='text-green-600'>Free</span>
                  ) : (
                    `$${deliveryFee.toFixed(2)}`
                  )}
                </span>
              </div>
              <div className='pt-3 border-t border-gray-100 flex justify-between font-semibold text-base'>
                <span>Total</span>
                <span>
                  {deliveryLoading ? (
                    <div className='animate-pulse bg-gray-200 h-4 w-16 rounded'></div>
                  ) : (
                    `$${(cart.subtotal + (deliveryFee || 0)).toFixed(2)}`
                  )}
                </span>
              </div>

              {/* Loyalty Points Preview */}
              {loyaltyData && (
                <div className='mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg'>
                  <div className='flex items-center justify-between text-sm'>
                    <span className='text-orange-800 font-medium'>
                      Points you'll earn:
                    </span>
                    <span className='text-orange-600 font-bold'>
                      +
                      {Math.floor(
                        cart.total *
                          LOYALTY_TIERS[loyaltyData.tier].pointsMultiplier
                      )}{" "}
                      pts
                    </span>
                  </div>
                  <div className='text-xs text-orange-600 mt-1'>
                    {LOYALTY_TIERS[loyaltyData.tier].name} member:{" "}
                    {LOYALTY_TIERS[loyaltyData.tier].pointsMultiplier}x points
                  </div>
                </div>
              )}
            </div>

            <div className='lg:hidden mt-6'>
              <Button
                type='button'
                variant='primary'
                size='large'
                fullWidth
                loading={loading}
                onClick={handleSubmit(onSubmit)}
              >
                Place Order
              </Button>
            </div>
          </Card>
        </div>
      </div>

      {/* Schedule Order Modal */}
      <ScheduleOrderModal
        isOpen={showScheduleModal}
        onClose={() => setShowScheduleModal(false)}
        restaurantId={cart.restaurantId}
        orderData={{
          items: cart.items,
          total: cart.total,
          restaurantName: cart.restaurantName,
        }}
        onSchedule={handleScheduleOrder}
      />
    </div>
  );
};

export default Checkout;
