import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  Image as ImageIcon,
  DollarSign,
  Clock,
  Tag,
  Eye,
  EyeOff,
  Save,
  X,
  AlertCircle,
  CheckCircle,
  Package,
  Grid,
  List,
  Upload,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import { menuCategoryApi, menuItemApi } from "../../utils/menuApi";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import Input from "../../components/common/Input";
import FormControl from "../../components/common/FormControl";

function RestaurantMenuManager() {
  const { restaurantId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { getRestaurant } = useRestaurant();

  // State management
  const [restaurant, setRestaurant] = useState(null);
  const [categories, setCategories] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // UI State
  const [activeTab, setActiveTab] = useState("categories");
  const [viewMode, setViewMode] = useState("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [showItemForm, setShowItemForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingItem, setEditingItem] = useState(null);

  // Form data
  const [categoryForm, setCategoryForm] = useState({
    name: "",
    description: "",
  });
  const [itemForm, setItemForm] = useState({
    name: "",
    description: "",
    price: "",
    preparation_time: "",
    is_vegetarian: false,
    is_available: true,
    category_id: "",
    image: null,
  });

  // Load restaurant data
  useEffect(() => {
    const loadRestaurantData = async () => {
      try {
        setLoading(true);

        // If no restaurantId in URL, get user's first restaurant
        if (!restaurantId) {
          const token = localStorage.getItem("afghanSofraUser");
          if (token) {
            const userData = JSON.parse(token);
            const response = await fetch(
              "http://127.0.0.1:8000/api/restaurant/restaurants/my_restaurants/",
              {
                headers: {
                  Authorization: `Bearer ${userData.access_token}`,
                  "Content-Type": "application/json",
                },
              }
            );

            if (response.ok) {
              const restaurants = await response.json();
              if (restaurants.length > 0) {
                // Use the first restaurant
                const firstRestaurant = restaurants[0];
                setRestaurant(firstRestaurant);
                // Redirect to URL with restaurant ID for consistency
                navigate(`/restaurant/menu-manager/${firstRestaurant.id}`, {
                  replace: true,
                });
                return;
              } else {
                setError("No restaurants found for this user");
                setLoading(false);
                return;
              }
            } else {
              setError("Failed to load restaurants");
              setLoading(false);
              return;
            }
          } else {
            setError("Authentication required");
            setLoading(false);
            return;
          }
        }

        // Load restaurant details
        const restaurantResult = await getRestaurant(restaurantId);
        if (restaurantResult.success) {
          setRestaurant(restaurantResult.data);
        } else {
          setError("Failed to load restaurant details");
          return;
        }

        // Load categories
        await loadCategories();

        // Load menu items
        await loadMenuItems();
      } catch (err) {
        setError("Failed to load restaurant data");
        console.error("Error loading restaurant data:", err);
      } finally {
        setLoading(false);
      }
    };

    loadRestaurantData();
  }, [restaurantId, getRestaurant]);

  // Load categories
  const loadCategories = async () => {
    try {
      const result = await menuCategoryApi.getCategories(restaurantId);
      if (result.success) {
        setCategories(result.data);
      } else {
        console.error("Failed to load categories:", result.error);
      }
    } catch (err) {
      console.error("Error loading categories:", err);
    }
  };

  // Load menu items
  const loadMenuItems = async () => {
    try {
      const result = await menuItemApi.getItemsByRestaurant(restaurantId);
      if (result.success) {
        setMenuItems(result.data);
      } else {
        console.error("Failed to load menu items:", result.error);
      }
    } catch (err) {
      console.error("Error loading menu items:", err);
    }
  };

  // Create category
  const handleCreateCategory = async (e) => {
    e.preventDefault();

    try {
      const result = await menuCategoryApi.createCategory({
        ...categoryForm,
        restaurant: restaurantId,
      });

      if (result.success) {
        setSuccess("Category created successfully!");
        setCategoryForm({ name: "", description: "" });
        setShowCategoryForm(false);
        await loadCategories();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("Failed to create category");
      console.error("Error creating category:", err);
    }
  };

  // Update category
  const handleUpdateCategory = async (e) => {
    e.preventDefault();

    try {
      const result = await menuCategoryApi.updateCategory(
        editingCategory.id,
        categoryForm
      );

      if (result.success) {
        setSuccess("Category updated successfully!");
        setCategoryForm({ name: "", description: "" });
        setEditingCategory(null);
        setShowCategoryForm(false);
        await loadCategories();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("Failed to update category");
      console.error("Error updating category:", err);
    }
  };

  // Delete category
  const handleDeleteCategory = async (categoryId) => {
    if (
      !confirm(
        "Are you sure you want to delete this category? All menu items in this category will also be deleted."
      )
    ) {
      return;
    }

    try {
      const result = await menuCategoryApi.deleteCategory(categoryId);

      if (result.success) {
        setSuccess("Category deleted successfully!");
        await loadCategories();
        await loadMenuItems();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("Failed to delete category");
      console.error("Error deleting category:", err);
    }
  };

  // Create menu item
  const handleCreateItem = async (e) => {
    e.preventDefault();

    try {
      console.log("Creating menu item with form data:", itemForm);

      // Validate required fields
      if (
        !itemForm.name ||
        !itemForm.description ||
        !itemForm.price ||
        !itemForm.preparation_time ||
        !itemForm.category_id
      ) {
        setError("Please fill in all required fields");
        return;
      }

      // Validate category exists
      const categoryExists = categories.find(
        (cat) => cat.id === parseInt(itemForm.category_id)
      );
      if (!categoryExists) {
        setError("Please select a valid category");
        return;
      }

      console.log("Selected category:", categoryExists);

      const result = await menuItemApi.createItem(itemForm);

      if (result.success) {
        setSuccess("Menu item created successfully!");
        setItemForm({
          name: "",
          description: "",
          price: "",
          preparation_time: "",
          is_vegetarian: false,
          is_available: true,
          category_id: "",
          image: null,
        });
        setShowItemForm(false);
        await loadMenuItems();
      } else {
        console.error("API Error:", result);
        setError(`Failed to create menu item: ${result.error}`);
        if (result.details) {
          console.error("Error details:", result.details);
        }
      }
    } catch (err) {
      setError("Failed to create menu item");
      console.error("Error creating menu item:", err);
    }
  };

  // Update menu item
  const handleUpdateItem = async (e) => {
    e.preventDefault();

    try {
      const formData = new FormData();
      Object.keys(itemForm).forEach((key) => {
        if (key === "image" && itemForm[key]) {
          formData.append(key, itemForm[key]);
        } else if (key !== "image") {
          formData.append(key, itemForm[key]);
        }
      });

      const result = await menuItemApi.updateItem(editingItem.id, formData);

      if (result.success) {
        setSuccess("Menu item updated successfully!");
        setItemForm({
          name: "",
          description: "",
          price: "",
          preparation_time: "",
          is_vegetarian: false,
          is_available: true,
          category_id: "",
          image: null,
        });
        setEditingItem(null);
        setShowItemForm(false);
        await loadMenuItems();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("Failed to update menu item");
      console.error("Error updating menu item:", err);
    }
  };

  // Delete menu item
  const handleDeleteItem = async (itemId) => {
    if (!confirm("Are you sure you want to delete this menu item?")) {
      return;
    }

    try {
      const result = await menuItemApi.deleteItem(itemId);

      if (result.success) {
        setSuccess("Menu item deleted successfully!");
        await loadMenuItems();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("Failed to delete menu item");
      console.error("Error deleting menu item:", err);
    }
  };

  // Edit category
  const startEditCategory = (category) => {
    setEditingCategory(category);
    setCategoryForm({
      name: category.name,
      description: category.description || "",
    });
    setShowCategoryForm(true);
  };

  // Edit menu item
  const startEditItem = (item) => {
    setEditingItem(item);
    setItemForm({
      name: item.name,
      description: item.description,
      price: item.price.toString(),
      preparation_time: item.preparation_time.toString(),
      is_vegetarian: item.is_vegetarian,
      is_available: item.is_available,
      category_id: item.category.toString(),
      image: null, // Don't pre-fill image
    });
    setShowItemForm(true);
  };

  // Clear messages
  useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null);
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  if (loading) {
    return (
      <div className='p-6'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-6'></div>
          <div className='space-y-4'>
            {[1, 2, 3].map((i) => (
              <div key={i} className='h-32 bg-gray-200 rounded'></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error && !restaurant) {
    return (
      <div className='p-6'>
        <Card className='p-8 text-center'>
          <AlertCircle className='mx-auto mb-4 text-red-500' size={48} />
          <h2 className='text-xl font-semibold mb-2'>
            Error Loading Restaurant
          </h2>
          <p className='text-gray-600 mb-4'>{error}</p>
          <Button
            variant='primary'
            onClick={() => navigate("/restaurant/my-restaurants")}
          >
            Back to Restaurants
          </Button>
        </Card>
      </div>
    );
  }

  // Filter menu items based on search and selected category
  const filteredItems = menuItems.filter((item) => {
    const matchesSearch =
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory =
      !selectedCategory || item.category === selectedCategory.id;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className='p-6 animate-fade-in'>
      {/* Header */}
      <div className='mb-8'>
        <div className='flex items-center justify-between mb-4'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>
              Menu Management - {restaurant?.name}
            </h1>
            <p className='text-gray-600'>
              Manage your restaurant's menu categories and items
            </p>
          </div>
          <div className='flex items-center space-x-3'>
            <Button
              variant='outline'
              onClick={() => navigate(`/restaurant/dashboard/${restaurantId}`)}
            >
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className='mb-6 p-4 bg-green-50 border border-green-200 rounded-lg'>
          <div className='flex items-center'>
            <CheckCircle className='text-green-500 mr-2' size={20} />
            <span className='text-green-700'>{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
          <div className='flex items-center'>
            <AlertCircle className='text-red-500 mr-2' size={20} />
            <span className='text-red-700'>{error}</span>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className='mb-6'>
        <div className='border-b border-gray-200'>
          <nav className='-mb-px flex space-x-8'>
            <button
              onClick={() => setActiveTab("categories")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "categories"
                  ? "border-primary-500 text-primary-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Tag className='inline mr-2' size={16} />
              Categories ({categories.length})
            </button>
            <button
              onClick={() => setActiveTab("items")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "items"
                  ? "border-primary-500 text-primary-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Package className='inline mr-2' size={16} />
              Menu Items ({menuItems.length})
            </button>
          </nav>
        </div>
      </div>

      {/* Categories Tab */}
      {activeTab === "categories" && (
        <div>
          {/* Categories Header */}
          <div className='flex items-center justify-between mb-6'>
            <h2 className='text-xl font-semibold'>Menu Categories</h2>
            <Button
              variant='primary'
              onClick={() => {
                setEditingCategory(null);
                setCategoryForm({ name: "", description: "" });
                setShowCategoryForm(true);
              }}
            >
              <Plus size={18} className='mr-2' />
              Add Category
            </Button>
          </div>

          {/* Categories List */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {categories.map((category) => (
              <Card key={category.id} className='p-6'>
                <div className='flex items-start justify-between mb-4'>
                  <div>
                    <h3 className='text-lg font-semibold text-gray-900'>
                      {category.name}
                    </h3>
                    {category.description && (
                      <p className='text-gray-600 text-sm mt-1'>
                        {category.description}
                      </p>
                    )}
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Button
                      variant='outline'
                      size='small'
                      onClick={() => startEditCategory(category)}
                    >
                      <Edit size={16} />
                    </Button>
                    <Button
                      variant='danger'
                      size='small'
                      onClick={() => handleDeleteCategory(category.id)}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>

                <div className='text-sm text-gray-500'>
                  {
                    menuItems.filter((item) => item.category === category.id)
                      .length
                  }{" "}
                  items
                </div>
              </Card>
            ))}
          </div>

          {categories.length === 0 && (
            <Card className='p-8 text-center'>
              <Tag className='mx-auto mb-4 text-gray-400' size={48} />
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                No Categories Yet
              </h3>
              <p className='text-gray-500 mb-4'>
                Create your first menu category to start organizing your menu
                items.
              </p>
              <Button
                variant='primary'
                onClick={() => {
                  setEditingCategory(null);
                  setCategoryForm({ name: "", description: "" });
                  setShowCategoryForm(true);
                }}
              >
                <Plus size={18} className='mr-2' />
                Add First Category
              </Button>
            </Card>
          )}
        </div>
      )}

      {/* Menu Items Tab */}
      {activeTab === "items" && (
        <div>
          {/* Items Header */}
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4'>
            <div className='flex items-center space-x-4'>
              <h2 className='text-xl font-semibold'>Menu Items</h2>
              <div className='flex items-center space-x-2'>
                <button
                  onClick={() => setViewMode("grid")}
                  className={`p-2 rounded ${
                    viewMode === "grid"
                      ? "bg-primary-100 text-primary-600"
                      : "text-gray-400"
                  }`}
                >
                  <Grid size={18} />
                </button>
                <button
                  onClick={() => setViewMode("list")}
                  className={`p-2 rounded ${
                    viewMode === "list"
                      ? "bg-primary-100 text-primary-600"
                      : "text-gray-400"
                  }`}
                >
                  <List size={18} />
                </button>
              </div>
            </div>

            <div className='flex items-center space-x-3'>
              <div className='relative'>
                <Search
                  className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
                  size={18}
                />
                <input
                  type='text'
                  placeholder='Search menu items...'
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className='pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                />
              </div>

              <select
                value={selectedCategory?.id || ""}
                onChange={(e) => {
                  const categoryId = e.target.value;
                  setSelectedCategory(
                    categoryId
                      ? categories.find((c) => c.id === parseInt(categoryId))
                      : null
                  );
                }}
                className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              >
                <option value=''>All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>

              <Button
                variant='primary'
                onClick={() => {
                  if (categories.length === 0) {
                    setError("Please create at least one category first");
                    return;
                  }
                  setEditingItem(null);
                  setItemForm({
                    name: "",
                    description: "",
                    price: "",
                    preparation_time: "",
                    is_vegetarian: false,
                    is_available: true,
                    category_id: categories[0]?.id || "",
                    image: null,
                  });
                  setShowItemForm(true);
                }}
              >
                <Plus size={18} className='mr-2' />
                Add Item
              </Button>
            </div>
          </div>

          {/* Items Grid/List */}
          {viewMode === "grid" ? (
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {filteredItems.map((item) => (
                <Card key={item.id} className='overflow-hidden'>
                  {item.image && (
                    <div className='h-48 bg-gray-200'>
                      <img
                        src={item.image}
                        alt={item.name}
                        className='w-full h-full object-cover'
                      />
                    </div>
                  )}
                  <div className='p-4'>
                    <div className='flex items-start justify-between mb-2'>
                      <h3 className='text-lg font-semibold text-gray-900'>
                        {item.name}
                      </h3>
                      <div className='flex items-center space-x-1'>
                        {item.is_available ? (
                          <Eye className='text-green-500' size={16} />
                        ) : (
                          <EyeOff className='text-gray-400' size={16} />
                        )}
                        {item.is_vegetarian && (
                          <Badge variant='success' size='small'>
                            Veg
                          </Badge>
                        )}
                      </div>
                    </div>

                    <p className='text-gray-600 text-sm mb-3 line-clamp-2'>
                      {item.description}
                    </p>

                    <div className='flex items-center justify-between mb-4'>
                      <div className='flex items-center space-x-4 text-sm text-gray-500'>
                        <div className='flex items-center'>
                          <DollarSign size={14} className='mr-1' />${item.price}
                        </div>
                        <div className='flex items-center'>
                          <Clock size={14} className='mr-1' />
                          {item.preparation_time}m
                        </div>
                      </div>
                    </div>

                    <div className='flex items-center space-x-2'>
                      <Button
                        variant='outline'
                        size='small'
                        onClick={() => startEditItem(item)}
                        className='flex-1'
                      >
                        <Edit size={16} className='mr-1' />
                        Edit
                      </Button>
                      <Button
                        variant='danger'
                        size='small'
                        onClick={() => handleDeleteItem(item.id)}
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div className='space-y-4'>
              {filteredItems.map((item) => (
                <Card key={item.id} className='p-4'>
                  <div className='flex items-center space-x-4'>
                    {item.image && (
                      <div className='w-16 h-16 bg-gray-200 rounded-lg overflow-hidden'>
                        <img
                          src={item.image}
                          alt={item.name}
                          className='w-full h-full object-cover'
                        />
                      </div>
                    )}

                    <div className='flex-1'>
                      <div className='flex items-start justify-between'>
                        <div>
                          <h3 className='text-lg font-semibold text-gray-900'>
                            {item.name}
                          </h3>
                          <p className='text-gray-600 text-sm'>
                            {item.description}
                          </p>
                        </div>

                        <div className='flex items-center space-x-4'>
                          <div className='text-right'>
                            <div className='text-lg font-semibold text-gray-900'>
                              ${item.price}
                            </div>
                            <div className='text-sm text-gray-500'>
                              {item.preparation_time}m prep
                            </div>
                          </div>

                          <div className='flex items-center space-x-2'>
                            {item.is_available ? (
                              <Badge variant='success'>Available</Badge>
                            ) : (
                              <Badge variant='secondary'>Unavailable</Badge>
                            )}
                            {item.is_vegetarian && (
                              <Badge variant='success'>Vegetarian</Badge>
                            )}
                          </div>

                          <div className='flex items-center space-x-2'>
                            <Button
                              variant='outline'
                              size='small'
                              onClick={() => startEditItem(item)}
                            >
                              <Edit size={16} />
                            </Button>
                            <Button
                              variant='danger'
                              size='small'
                              onClick={() => handleDeleteItem(item.id)}
                            >
                              <Trash2 size={16} />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {filteredItems.length === 0 && (
            <Card className='p-8 text-center'>
              <Package className='mx-auto mb-4 text-gray-400' size={48} />
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                {searchQuery || selectedCategory
                  ? "No items found"
                  : "No Menu Items Yet"}
              </h3>
              <p className='text-gray-500 mb-4'>
                {searchQuery || selectedCategory
                  ? "Try adjusting your search or filter criteria"
                  : "Add your first menu item to start building your menu"}
              </p>
              {!searchQuery && !selectedCategory && (
                <Button
                  variant='primary'
                  onClick={() => {
                    if (categories.length === 0) {
                      setError("Please create at least one category first");
                      return;
                    }
                    setEditingItem(null);
                    setItemForm({
                      name: "",
                      description: "",
                      price: "",
                      preparation_time: "",
                      is_vegetarian: false,
                      is_available: true,
                      category_id: categories[0]?.id || "",
                      image: null,
                    });
                    setShowItemForm(true);
                  }}
                >
                  <Plus size={18} className='mr-2' />
                  Add First Item
                </Button>
              )}
            </Card>
          )}
        </div>
      )}

      {/* Category Form Modal */}
      {showCategoryForm && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg p-6 w-full max-w-md'>
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg font-semibold'>
                {editingCategory ? "Edit Category" : "Add Category"}
              </h3>
              <button
                onClick={() => {
                  setShowCategoryForm(false);
                  setEditingCategory(null);
                  setCategoryForm({ name: "", description: "" });
                }}
                className='text-gray-400 hover:text-gray-600'
              >
                <X size={20} />
              </button>
            </div>

            <form
              onSubmit={
                editingCategory ? handleUpdateCategory : handleCreateCategory
              }
            >
              <div className='space-y-4'>
                <FormControl label='Category Name' required>
                  <Input
                    type='text'
                    value={categoryForm.name}
                    onChange={(e) =>
                      setCategoryForm({ ...categoryForm, name: e.target.value })
                    }
                    placeholder='e.g., Appetizers, Main Dishes, Desserts'
                    required
                  />
                </FormControl>

                <FormControl label='Description'>
                  <textarea
                    value={categoryForm.description}
                    onChange={(e) =>
                      setCategoryForm({
                        ...categoryForm,
                        description: e.target.value,
                      })
                    }
                    placeholder='Brief description of this category'
                    rows={3}
                    className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                  />
                </FormControl>
              </div>

              <div className='flex items-center justify-end space-x-3 mt-6'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => {
                    setShowCategoryForm(false);
                    setEditingCategory(null);
                    setCategoryForm({ name: "", description: "" });
                  }}
                >
                  Cancel
                </Button>
                <Button type='submit' variant='primary'>
                  <Save size={18} className='mr-2' />
                  {editingCategory ? "Update" : "Create"} Category
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Menu Item Form Modal */}
      {showItemForm && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto'>
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg font-semibold'>
                {editingItem ? "Edit Menu Item" : "Add Menu Item"}
              </h3>
              <button
                onClick={() => {
                  setShowItemForm(false);
                  setEditingItem(null);
                  setItemForm({
                    name: "",
                    description: "",
                    price: "",
                    preparation_time: "",
                    is_vegetarian: false,
                    is_available: true,
                    category_id: "",
                    image: null,
                  });
                }}
                className='text-gray-400 hover:text-gray-600'
              >
                <X size={20} />
              </button>
            </div>

            <form onSubmit={editingItem ? handleUpdateItem : handleCreateItem}>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <FormControl label='Item Name' required>
                  <Input
                    type='text'
                    value={itemForm.name}
                    onChange={(e) =>
                      setItemForm({ ...itemForm, name: e.target.value })
                    }
                    placeholder='e.g., Chicken Tikka Masala'
                    required
                  />
                </FormControl>

                <FormControl label='Category' required>
                  <select
                    value={itemForm.category_id}
                    onChange={(e) =>
                      setItemForm({ ...itemForm, category_id: e.target.value })
                    }
                    required
                    className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                  >
                    <option value=''>Select Category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </FormControl>

                <FormControl label='Price ($)' required>
                  <Input
                    type='number'
                    step='0.01'
                    min='0'
                    value={itemForm.price}
                    onChange={(e) =>
                      setItemForm({ ...itemForm, price: e.target.value })
                    }
                    placeholder='0.00'
                    required
                  />
                </FormControl>

                <FormControl label='Preparation Time (minutes)' required>
                  <Input
                    type='number'
                    min='1'
                    value={itemForm.preparation_time}
                    onChange={(e) =>
                      setItemForm({
                        ...itemForm,
                        preparation_time: e.target.value,
                      })
                    }
                    placeholder='15'
                    required
                  />
                </FormControl>
              </div>

              <div className='mt-4'>
                <FormControl label='Description' required>
                  <textarea
                    value={itemForm.description}
                    onChange={(e) =>
                      setItemForm({ ...itemForm, description: e.target.value })
                    }
                    placeholder='Detailed description of the menu item'
                    rows={3}
                    required
                    className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                  />
                </FormControl>
              </div>

              <div className='mt-4'>
                <FormControl label='Image'>
                  <div className='flex items-center space-x-4'>
                    <input
                      type='file'
                      accept='image/*'
                      onChange={(e) =>
                        setItemForm({ ...itemForm, image: e.target.files[0] })
                      }
                      className='hidden'
                      id='item-image'
                    />
                    <label
                      htmlFor='item-image'
                      className='flex items-center px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50'
                    >
                      <Upload size={18} className='mr-2' />
                      Choose Image
                    </label>
                    {itemForm.image && (
                      <span className='text-sm text-gray-600'>
                        {itemForm.image.name}
                      </span>
                    )}
                  </div>
                </FormControl>
              </div>

              <div className='mt-4 flex items-center space-x-6'>
                <label className='flex items-center'>
                  <input
                    type='checkbox'
                    checked={itemForm.is_vegetarian}
                    onChange={(e) =>
                      setItemForm({
                        ...itemForm,
                        is_vegetarian: e.target.checked,
                      })
                    }
                    className='mr-2'
                  />
                  Vegetarian
                </label>

                <label className='flex items-center'>
                  <input
                    type='checkbox'
                    checked={itemForm.is_available}
                    onChange={(e) =>
                      setItemForm({
                        ...itemForm,
                        is_available: e.target.checked,
                      })
                    }
                    className='mr-2'
                  />
                  Available
                </label>
              </div>

              <div className='flex items-center justify-end space-x-3 mt-6'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => {
                    setShowItemForm(false);
                    setEditingItem(null);
                    setItemForm({
                      name: "",
                      description: "",
                      price: "",
                      preparation_time: "",
                      is_vegetarian: false,
                      is_available: true,
                      category_id: "",
                      image: null,
                    });
                  }}
                >
                  Cancel
                </Button>
                <Button type='submit' variant='primary'>
                  <Save size={18} className='mr-2' />
                  {editingItem ? "Update" : "Create"} Item
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default RestaurantMenuManager;
