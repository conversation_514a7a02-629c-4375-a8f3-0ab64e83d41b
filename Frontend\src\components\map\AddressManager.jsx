import React, { useState, useEffect } from "react";
import {
  MapPin,
  Home,
  Briefcase,
  Plus,
  Edit2,
  Trash2,
  Navigation,
} from "lucide-react";
import Button from "../common/Button";
import Card from "../common/Card";
import Badge from "../common/Badge";
import LocationPicker from "./LocationPicker";
import { cn } from "../../utils/cn";
import axios from "axios";

// Create axios instance for address API
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000/api",
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
    if (user.access_token) {
      config.headers.Authorization = `Bearer ${user.access_token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Helper function to get postal code based on city
const getPostalCodeForCity = (city) => {
  const cityLower = city.toLowerCase();
  if (cityLower.includes("kabul")) return "1001";
  if (cityLower.includes("herat")) return "3001";
  if (cityLower.includes("kandahar")) return "3801";
  if (cityLower.includes("mazar")) return "1801";
  if (cityLower.includes("jalalabad")) return "2601";
  return "1001"; // Default to Kabul
};

// Helper function to format address from Nominatim data
const formatLocationAddress = (data) => {
  if (!data || !data.address) return null;

  const address = data.address;
  const parts = [];

  // Add house number and road
  if (address.house_number && address.road) {
    parts.push(`${address.house_number} ${address.road}`);
  } else if (address.road) {
    parts.push(address.road);
  }

  // Add neighborhood or suburb
  if (address.neighbourhood) {
    parts.push(address.neighbourhood);
  } else if (address.suburb) {
    parts.push(address.suburb);
  }

  // Add city
  if (address.city) {
    parts.push(address.city);
  } else if (address.town) {
    parts.push(address.town);
  } else if (address.village) {
    parts.push(address.village);
  }

  // Add country
  if (address.country) {
    parts.push(address.country);
  }

  return parts.length > 0 ? parts.join(", ") : data.display_name;
};

// Helper function to calculate distance between two coordinates (in km)
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the Earth in km
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

// Helper function to format address for display
const formatAddressDisplay = (address) => {
  if (!address) return "";

  // If it's a coordinate-based address, format it nicely
  if (address.address && address.address.includes(",")) {
    const parts = address.address.split(",").map((part) => part.trim());
    return parts.join(" • ");
  }

  return address.address || address.label || "Unknown Address";
};

// Helper function to get address type display
const getAddressTypeDisplay = (type) => {
  switch (type) {
    case "saved":
      return { label: "Saved", color: "bg-green-100 text-green-800" };
    case "current":
      return { label: "Current", color: "bg-blue-100 text-blue-800" };
    case "home":
      return { label: "Home", color: "bg-purple-100 text-purple-800" };
    case "work":
      return { label: "Work", color: "bg-orange-100 text-orange-800" };
    default:
      return { label: "Other", color: "bg-gray-100 text-gray-800" };
  }
};

const AddressManager = ({
  onAddressSelect,
  selectedAddressId,
  className = "",
  showAddButton = true,
  title = "Delivery Address",
}) => {
  const [addresses, setAddresses] = useState([]);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load addresses from backend API
  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log(
        "📍 AddressManager: Fetching addresses from API and localStorage..."
      );

      // Get addresses from localStorage (for current location and temporary addresses)
      const localAddresses = JSON.parse(
        localStorage.getItem("afghanSofraAddresses") || "[]"
      );
      console.log(
        "📍 AddressManager: Found localStorage addresses:",
        localAddresses
      );

      // Get addresses from API (for saved addresses)
      let apiAddresses = [];
      try {
        const response = await apiClient.get("/restaurant/addresses/");
        if (response.data && Array.isArray(response.data)) {
          apiAddresses = response.data.map((addr) => ({
            id: addr.id, // Use actual backend ID without prefix
            backendId: addr.id, // Store original backend ID for API calls
            type: "saved",
            label: `${addr.street}, ${addr.city}`,
            address: `${addr.street}, ${addr.city}, ${addr.state}, ${addr.country}`,
            coordinates: [
              parseFloat(addr.latitude),
              parseFloat(addr.longitude),
            ],
            isDefault: false, // Backend doesn't have default concept yet
            street: addr.street,
            city: addr.city,
            state: addr.state,
            country: addr.country,
            postal_code: addr.postal_code,
            source: "api",
          }));
          console.log("📍 AddressManager: Found API addresses:", apiAddresses);
        }
      } catch (apiError) {
        console.log(
          "📍 AddressManager: API addresses not available (user might not be logged in):",
          apiError.message
        );
        // Continue with localStorage addresses only
      }

      // Combine and deduplicate addresses
      const allAddresses = [...localAddresses, ...apiAddresses];

      // Remove duplicates based on coordinates (within 100m)
      const uniqueAddresses = allAddresses.filter((addr, index, arr) => {
        return !arr.slice(0, index).some((existingAddr) => {
          if (!addr.coordinates || !existingAddr.coordinates) return false;
          const distance = calculateDistance(
            addr.coordinates[0],
            addr.coordinates[1],
            existingAddr.coordinates[0],
            existingAddr.coordinates[1]
          );
          return distance < 0.1; // Less than 100m apart
        });
      });

      console.log(
        "📍 AddressManager: Final unique addresses:",
        uniqueAddresses
      );
      setAddresses(uniqueAddresses);

      // Auto-select first address if none selected and addresses exist
      if (uniqueAddresses.length > 0 && !selectedAddressId && onAddressSelect) {
        onAddressSelect(uniqueAddresses[0]);
      }
    } catch (error) {
      console.error("Error fetching addresses:", error);
      setError("Failed to load addresses");

      // Fallback: create a default address if none exist
      if (error.response?.status === 401) {
        setError("Please log in to manage addresses");
      } else {
        // Try to create a default address
        await createDefaultAddress();
      }
    } finally {
      setLoading(false);
    }
  };

  const createDefaultAddress = async () => {
    try {
      const defaultAddressData = {
        street: "123 Main Street",
        city: "Kabul",
        state: "Kabul",
        postal_code: "1001",
        country: "Afghanistan",
        latitude: "34.5553",
        longitude: "69.2075",
      };

      const response = await apiClient.post(
        "/restaurant/addresses/",
        defaultAddressData
      );

      if (response.data) {
        console.log("Created default address:", response.data);
        // Refresh addresses after creating default
        await fetchAddresses();
      }
    } catch (error) {
      console.error("Error creating default address:", error);
    }
  };

  // Add new address via API with better data parsing
  const handleAddAddress = async (locationData) => {
    try {
      console.log("📍 AddressManager: Adding new address:", locationData);

      // Validate input data
      if (!locationData || !locationData.address || !locationData.coordinates) {
        throw new Error("Invalid location data provided");
      }

      if (
        !Array.isArray(locationData.coordinates) ||
        locationData.coordinates.length !== 2
      ) {
        throw new Error("Invalid coordinates provided");
      }

      // Parse address more intelligently
      const addressParts = locationData.address
        .split(",")
        .map((part) => part.trim())
        .filter((part) => part.length > 0);

      const street = addressParts[0] || "New Address";
      const city =
        addressParts.find(
          (part) =>
            part.toLowerCase().includes("kabul") ||
            part.toLowerCase().includes("herat") ||
            part.toLowerCase().includes("kandahar") ||
            part.toLowerCase().includes("mazar")
        ) ||
        addressParts[1] ||
        "Kabul";

      // Validate coordinates
      const latitude = parseFloat(locationData.coordinates[0]);
      const longitude = parseFloat(locationData.coordinates[1]);

      if (isNaN(latitude) || isNaN(longitude)) {
        throw new Error("Invalid coordinates provided");
      }

      const addressData = {
        street: street.substring(0, 255), // Ensure it fits in database
        city: city.substring(0, 100),
        state: city.includes("Kabul") ? "Kabul Province" : "Afghanistan",
        postal_code: getPostalCodeForCity(city),
        country: "Afghanistan",
        latitude: latitude.toString(),
        longitude: longitude.toString(),
      };

      console.log("📍 AddressManager: Sending address data:", addressData);

      const response = await apiClient.post(
        "/restaurant/addresses/",
        addressData
      );

      if (response.data) {
        console.log("Address created successfully:", response.data);

        // Transform the new address to frontend format
        const newAddress = {
          id: response.data.id,
          backendId: response.data.id, // Store backend ID for API calls
          type: "saved", // Mark as saved address
          label: `${response.data.street}, ${response.data.city}`,
          address: `${response.data.street}, ${response.data.city}, ${response.data.state}, ${response.data.country}`,
          coordinates: [
            parseFloat(response.data.latitude),
            parseFloat(response.data.longitude),
          ],
          isDefault: false,
          street: response.data.street,
          city: response.data.city,
          state: response.data.state,
          country: response.data.country,
          postal_code: response.data.postal_code,
          source: "api",
        };

        // Update local state
        setAddresses((prev) => [...prev, newAddress]);
        setShowLocationPicker(false);

        // Auto-select the new address
        onAddressSelect?.(newAddress);
      }
    } catch (error) {
      console.error("Error creating address:", error);

      // Provide specific error messages based on error type
      let errorMessage = "Failed to create address";

      if (error.message.includes("Invalid")) {
        errorMessage = error.message;
      } else if (error.response?.status === 400) {
        errorMessage =
          "Invalid address data. Please check your input and try again.";
      } else if (error.response?.status === 401) {
        errorMessage = "Please log in to add addresses.";
      } else if (error.response?.status === 409) {
        errorMessage = "This address already exists in your saved addresses.";
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message.includes("Network")) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      }

      setError(errorMessage);
    }
  };

  // Edit existing address via API
  const handleEditAddress = async (locationData) => {
    try {
      const addressData = {
        street: locationData.address.split(",")[0] || editingAddress.street,
        city: locationData.address.split(",")[1]?.trim() || editingAddress.city,
        state: editingAddress.state || "Kabul",
        postal_code: editingAddress.postal_code || "1001",
        country: editingAddress.country || "Afghanistan",
        latitude: locationData.coordinates[0].toString(),
        longitude: locationData.coordinates[1].toString(),
      };

      // Use backend ID for API call
      const backendId = editingAddress.backendId || editingAddress.id;
      const response = await apiClient.put(
        `/restaurant/addresses/${backendId}/`,
        addressData
      );

      if (response.data) {
        console.log("Address updated successfully:", response.data);

        // Update local state
        const updatedAddresses = addresses.map((addr) =>
          addr.id === editingAddress.id
            ? {
                ...addr,
                address: `${response.data.street}, ${response.data.city}, ${response.data.state}, ${response.data.country}`,
                coordinates: [
                  parseFloat(response.data.latitude),
                  parseFloat(response.data.longitude),
                ],
                street: response.data.street,
                city: response.data.city,
                state: response.data.state,
                country: response.data.country,
                postal_code: response.data.postal_code,
              }
            : addr
        );

        setAddresses(updatedAddresses);
        setEditingAddress(null);
        setShowLocationPicker(false);
      }
    } catch (error) {
      console.error("Error updating address:", error);
      setError("Failed to update address");
    }
  };

  // Delete address via API
  const handleDeleteAddress = async (addressId) => {
    try {
      // Find the address to get the backend ID
      const addressToDelete = addresses.find((addr) => addr.id === addressId);
      const backendId = addressToDelete?.backendId || addressId;

      await apiClient.delete(`/restaurant/addresses/${backendId}/`);

      // Update local state
      const updatedAddresses = addresses.filter(
        (addr) => addr.id !== addressId
      );
      setAddresses(updatedAddresses);

      console.log("Address deleted successfully");
    } catch (error) {
      console.error("Error deleting address:", error);
      setError("Failed to delete address");
    }
  };

  // Set default address (for future implementation)
  const handleSetDefault = (addressId) => {
    // This could be implemented when backend supports default addresses
    console.log("Set default address:", addressId);
  };

  // Get current location and add as address with better error handling
  const handleGetCurrentLocation = () => {
    setIsGettingLocation(true);
    console.log("📍 AddressManager: Getting current location...");

    if (!navigator.geolocation) {
      console.error("🚫 Geolocation is not supported by this browser");
      alert(
        "Geolocation is not supported by this browser. Please add your address manually."
      );
      setIsGettingLocation(false);
      return;
    }

    const options = {
      enableHighAccuracy: true,
      timeout: 10000, // 10 seconds timeout
      maximumAge: 60000, // Accept cached position up to 1 minute old
    };

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        console.log(
          `📍 AddressManager: Got location: ${latitude}, ${longitude} (accuracy: ${accuracy}m)`
        );

        try {
          // Get address from coordinates with better error handling
          const response = await fetch(
            `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1&accept-language=en`,
            {
              headers: {
                "User-Agent": "Afghan-Sofra-App/1.0",
              },
            }
          );

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          console.log("📍 AddressManager: Reverse geocoding result:", data);

          // Format address professionally
          const formattedAddress =
            formatLocationAddress(data) ||
            `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;

          const newAddress = {
            id: `current_${Date.now()}`,
            type: "current",
            label: "Current Location",
            address: formattedAddress,
            coordinates: [latitude, longitude],
            isDefault: addresses.length === 0,
            accuracy: Math.round(accuracy),
            createdAt: Date.now(),
            source: "geolocation",
          };

          console.log(
            "📍 AddressManager: Adding current location address:",
            newAddress
          );

          // Save to localStorage for persistence
          const existingAddresses = JSON.parse(
            localStorage.getItem("afghanSofraAddresses") || "[]"
          );
          const updatedLocalAddresses = [...existingAddresses, newAddress];
          localStorage.setItem(
            "afghanSofraAddresses",
            JSON.stringify(updatedLocalAddresses)
          );

          // Update component state
          setAddresses((prev) => [...prev, newAddress]);
          onAddressSelect?.(newAddress);

          // Show success message
          console.log(
            "✅ Current location added successfully and saved to localStorage"
          );
        } catch (error) {
          console.error("🚫 AddressManager: Error getting address:", error);

          // Still add the location with coordinates as fallback
          const fallbackAddress = `${latitude.toFixed(6)}, ${longitude.toFixed(
            6
          )}`;
          const newAddress = {
            id: `current_${Date.now()}`,
            type: "current",
            label: "Current Location",
            address: fallbackAddress,
            coordinates: [latitude, longitude],
            isDefault: addresses.length === 0,
            accuracy: Math.round(accuracy),
            createdAt: Date.now(),
            source: "geolocation_fallback",
          };

          // Save to localStorage for persistence
          const existingAddresses = JSON.parse(
            localStorage.getItem("afghanSofraAddresses") || "[]"
          );
          const updatedLocalAddresses = [...existingAddresses, newAddress];
          localStorage.setItem(
            "afghanSofraAddresses",
            JSON.stringify(updatedLocalAddresses)
          );

          // Update component state
          setAddresses((prev) => [...prev, newAddress]);
          onAddressSelect?.(newAddress);

          console.log(
            "⚠️ Added location with coordinates as fallback and saved to localStorage"
          );
        } finally {
          setIsGettingLocation(false);
        }
      },
      (error) => {
        console.error("🚫 AddressManager: Geolocation error:", error);

        let errorMessage =
          "Unable to get your location. Please add address manually.";

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage =
              "Location access denied. Please enable location permissions and try again.";
            console.log("🚫 User denied location permission");
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage =
              "Location information is unavailable. Please add address manually.";
            console.log("🚫 Location information unavailable");
            break;
          case error.TIMEOUT:
            errorMessage =
              "Location request timed out. Please try again or add address manually.";
            console.log("🚫 Location request timed out");
            break;
          default:
            console.log("🚫 Unknown geolocation error");
            break;
        }

        alert(errorMessage);
        setIsGettingLocation(false);
      },
      options
    );
  };

  const getAddressIcon = (type) => {
    switch (type) {
      case "home":
        return <Home size={16} />;
      case "work":
        return <Briefcase size={16} />;
      case "current":
        return <Navigation size={16} />;
      default:
        return <MapPin size={16} />;
    }
  };

  const getAddressColor = (type) => {
    switch (type) {
      case "home":
        return "text-blue-600 bg-blue-100";
      case "work":
        return "text-green-600 bg-green-100";
      case "current":
        return "text-purple-600 bg-purple-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  return (
    <div className={className}>
      {/* Modern Professional Header */}
      <div className='mb-6'>
        <div className='flex items-center justify-between mb-2'>
          <div>
            <h3 className='text-xl font-bold text-gray-900'>{title}</h3>
            <p className='text-gray-600 text-sm mt-1'>
              Manage your delivery addresses for faster checkout
            </p>
          </div>
          {showAddButton && (
            <div className='flex space-x-3'>
              <Button
                variant='outline'
                size='medium'
                onClick={handleGetCurrentLocation}
                disabled={isGettingLocation}
                icon={<Navigation size={16} />}
                className='bg-white hover:bg-blue-50 border-blue-200 text-blue-700 hover:border-blue-300 transition-all duration-200 shadow-sm'
              >
                {isGettingLocation ? "Locating..." : "Use Current Location"}
              </Button>
              <Button
                variant='primary'
                size='medium'
                onClick={() => setShowLocationPicker(true)}
                icon={<Plus size={16} />}
                className='bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-200'
              >
                Add New Address
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Professional Error Message */}
      {error && (
        <div className='mb-6 p-4 bg-red-50 border-l-4 border-red-400 rounded-r-lg shadow-sm'>
          <div className='flex items-start'>
            <div className='flex-shrink-0'>
              <MapPin size={20} className='text-red-500 mt-0.5' />
            </div>
            <div className='ml-3 flex-1'>
              <p className='text-red-800 font-medium text-sm'>Address Error</p>
              <p className='text-red-700 text-sm mt-1'>{error}</p>
              {error.includes("Failed to load") && (
                <div className='mt-2 text-xs text-red-600'>
                  <p>• Check your internet connection</p>
                  <p>• Try refreshing the page</p>
                  <p>• Contact support if the problem persists</p>
                </div>
              )}
              {error.includes("Please log in") && (
                <p className='text-red-600 text-xs mt-2'>
                  Please log in to your account to manage addresses.
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Modern Loading State */}
      {loading ? (
        <div className='space-y-4'>
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className='animate-pulse bg-white rounded-xl border border-gray-200 p-6 shadow-sm'
            >
              <div className='flex items-start space-x-4'>
                <div className='w-12 h-12 bg-gray-200 rounded-full'></div>
                <div className='flex-1 space-y-3'>
                  <div className='h-4 bg-gray-200 rounded w-1/3'></div>
                  <div className='h-3 bg-gray-200 rounded w-2/3'></div>
                  <div className='h-3 bg-gray-200 rounded w-1/2'></div>
                </div>
                <div className='flex space-x-2'>
                  <div className='w-8 h-8 bg-gray-200 rounded-full'></div>
                  <div className='w-8 h-8 bg-gray-200 rounded-full'></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <>
          {/* Address List */}
          <div className='space-y-3'>
            {addresses.length === 0 ? (
              <div className='text-center py-12 px-6'>
                <div className='bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100'>
                  <div className='w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4'>
                    <MapPin size={32} className='text-white' />
                  </div>
                  <h4 className='text-lg font-semibold text-gray-900 mb-2'>
                    No addresses yet
                  </h4>
                  <p className='text-gray-600 mb-6 max-w-sm mx-auto'>
                    Add your first delivery address to get started with fast and
                    convenient ordering
                  </p>
                  <div className='flex flex-col sm:flex-row gap-3 justify-center'>
                    <Button
                      variant='primary'
                      onClick={() => setShowLocationPicker(true)}
                      icon={<Plus size={16} />}
                      className='bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700'
                    >
                      Add Address
                    </Button>
                    <Button
                      variant='outline'
                      onClick={handleGetCurrentLocation}
                      disabled={isGettingLocation}
                      icon={<Navigation size={16} />}
                      className='border-blue-200 text-blue-700 hover:bg-blue-50'
                    >
                      {isGettingLocation
                        ? "Locating..."
                        : "Use Current Location"}
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              addresses.map((address) => (
                <div
                  key={address.id}
                  className={cn(
                    "group relative bg-white rounded-xl border-2 transition-all duration-300 cursor-pointer overflow-hidden",
                    selectedAddressId === address.id
                      ? "border-blue-500 bg-blue-50 shadow-lg ring-4 ring-blue-100"
                      : "border-gray-200 hover:border-blue-300 hover:shadow-lg hover:bg-gray-50"
                  )}
                  onClick={() => onAddressSelect?.(address)}
                >
                  {/* Selection Indicator */}
                  {selectedAddressId === address.id && (
                    <div className='absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-500'></div>
                  )}

                  <div className='p-6'>
                    <div className='flex items-start justify-between'>
                      <div className='flex items-start space-x-4 flex-1'>
                        {/* Modern Icon */}
                        <div
                          className={cn(
                            "flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-200",
                            selectedAddressId === address.id
                              ? "bg-blue-500 text-white shadow-lg"
                              : "bg-gray-100 text-gray-600 group-hover:bg-blue-100 group-hover:text-blue-600"
                          )}
                        >
                          {getAddressIcon(address.type)}
                        </div>

                        <div className='flex-1 min-w-0'>
                          {/* Address Header */}
                          <div className='flex items-center space-x-3 mb-2'>
                            <h4 className='font-semibold text-gray-900 text-lg truncate'>
                              {address.label}
                            </h4>
                            {address.isDefault && (
                              <Badge
                                variant='primary'
                                size='small'
                                className='bg-green-100 text-green-800 border-green-200'
                              >
                                Default
                              </Badge>
                            )}
                            {/* Address Type Badge */}
                            {(() => {
                              const typeDisplay = getAddressTypeDisplay(
                                address.type
                              );
                              return (
                                <span
                                  className={cn(
                                    "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                                    typeDisplay.color
                                  )}
                                >
                                  {typeDisplay.label}
                                </span>
                              );
                            })()}
                            {address.accuracy && (
                              <Badge
                                variant='outline'
                                size='small'
                                className='text-xs text-gray-500 border-gray-300'
                              >
                                ±{address.accuracy}m
                              </Badge>
                            )}
                          </div>

                          {/* Address Details */}
                          <p className='text-gray-600 text-sm leading-relaxed mb-3 line-clamp-2'>
                            {formatAddressDisplay(address)}
                          </p>
                        </div>
                      </div>

                      {/* Modern Action Buttons */}
                      <div className='flex items-center space-x-2 ml-4'>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingAddress(address);
                            setShowLocationPicker(true);
                          }}
                          className='p-2.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 group-hover:bg-white'
                          title='Edit address'
                        >
                          <Edit2 size={16} />
                        </button>

                        {addresses.length > 1 && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (
                                confirm(
                                  "Are you sure you want to delete this address?"
                                )
                              ) {
                                handleDeleteAddress(address.id);
                              }
                            }}
                            className='p-2.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 group-hover:bg-white'
                            title='Delete address'
                          >
                            <Trash2 size={16} />
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Set as Default Button */}
                    {!address.isDefault && (
                      <div className='mt-4 pt-4 border-t border-gray-100'>
                        <Button
                          variant='ghost'
                          size='small'
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSetDefault(address.id);
                          }}
                          className='text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-medium'
                        >
                          Set as Default
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </>
      )}

      {/* Location Picker Modal */}
      {showLocationPicker && (
        <LocationPicker
          onLocationSelect={
            editingAddress ? handleEditAddress : handleAddAddress
          }
          onClose={() => {
            setShowLocationPicker(false);
            setEditingAddress(null);
          }}
          initialLocation={editingAddress?.coordinates}
          title={editingAddress ? "Edit Address" : "Add New Address"}
        />
      )}
    </div>
  );
};

export default AddressManager;
