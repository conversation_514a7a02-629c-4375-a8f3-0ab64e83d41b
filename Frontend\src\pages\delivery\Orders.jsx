import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  TruckIcon,
  CheckCircle,
  Clock,
  MapPin,
  Search,
  Filter,
  Package,
  Navigation,
  Phone,
  MessageCircle,
  User,
  ChevronDown,
  ChevronUp,
  Calendar,
  DollarSign,
  XCircle,
  AlertCircle,
  CreditCard,
  Truck,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";
import { toast } from "react-hot-toast";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";

function DeliveryOrders() {
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedOrderId, setExpandedOrderId] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // Parse query parameters
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const statusParam = params.get("status");
    if (statusParam) {
      setActiveTab(statusParam);
    }
  }, [location]);

  useEffect(() => {
    fetchOrders();
  }, [user]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      console.log("=== FETCHING REAL ORDERS ===");
      console.log("Current user:", user);

      const response = await deliveryAgentApi.getMyOrders();

      if (response.data.status === "success") {
        const ordersData = response.data.data.orders;
        console.log("Fetched orders:", ordersData);
        setOrders(ordersData);
      } else {
        console.error("Failed to fetch orders:", response.data.message);
        toast.error("Failed to fetch orders");
        setOrders([]);
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error("Error fetching orders");
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter orders when tab changes or search query changes
  useEffect(() => {
    if (!orders.length) return;

    let filtered = [...orders];

    // Filter by status
    if (activeTab !== "all") {
      filtered = filtered.filter((order) => {
        if (activeTab === "active") {
          return ["assigned", "pickedUp"].includes(order.status);
        } else if (activeTab === "available") {
          return order.status === "available";
        } else if (activeTab === "completed") {
          return order.status === "delivered";
        }
        return order.status === activeTab;
      });
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (order) =>
          (order.id && order.id.toLowerCase().includes(query)) ||
          (order.customerName &&
            order.customerName.toLowerCase().includes(query)) ||
          (order.restaurantName &&
            order.restaurantName.toLowerCase().includes(query)) ||
          (order.pickupAddress &&
            order.pickupAddress.toLowerCase().includes(query)) ||
          (order.deliveryAddress &&
            order.deliveryAddress.toLowerCase().includes(query))
      );
    }

    setFilteredOrders(filtered);
  }, [orders, activeTab, searchQuery]);

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCurrency = (amount) => {
    return `$${amount.toFixed(2)}`;
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "assigned":
        return <Badge className='bg-blue-100 text-blue-800'>Assigned</Badge>;
      case "pickedUp":
        return (
          <Badge className='bg-yellow-100 text-yellow-800'>Picked Up</Badge>
        );
      case "delivered":
        return <Badge className='bg-green-100 text-green-800'>Delivered</Badge>;
      case "available":
        return (
          <Badge className='bg-purple-100 text-purple-800'>Available</Badge>
        );
      default:
        return <Badge className='bg-gray-100 text-gray-800'>{status}</Badge>;
    }
  };

  const toggleOrderExpand = (orderId) => {
    setExpandedOrderId(expandedOrderId === orderId ? null : orderId);
  };

  const handleViewOrderDetails = (order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  const handleAcceptOrder = async (order) => {
    try {
      const result = await deliveryAgentApi.acceptOrder(order.id);
      if (result.success) {
        alert(`Order #${order.id} accepted successfully!`);
        // Refresh orders data
        await loadOrdersData();
      } else {
        alert(`Failed to accept order: ${result.error}`);
      }
    } catch (err) {
      console.error("Error accepting order:", err);
      alert(`Error accepting order: ${err.message}`);
    }
  };

  const handlePickUpOrder = async (order) => {
    try {
      const result = await deliveryAgentApi.updateOrderStatus(
        order.id,
        "picked_up"
      );
      if (result.success) {
        alert(`Order #${order.id} picked up successfully!`);
        // Refresh orders data
        await loadOrdersData();
      } else {
        alert(`Failed to pick up order: ${result.error}`);
      }
    } catch (err) {
      console.error("Error picking up order:", err);
      alert(`Error picking up order: ${err.message}`);
    }
  };

  const handleDeliverOrder = async (order) => {
    try {
      const result = await deliveryAgentApi.updateOrderStatus(
        order.id,
        "delivered"
      );
      if (result.success) {
        alert(`Order #${order.id} delivered successfully!`);
        // Refresh orders data
        await loadOrdersData();
      } else {
        alert(`Failed to deliver order: ${result.error}`);
      }
    } catch (err) {
      console.error("Error delivering order:", err);
      alert(`Error delivering order: ${err.message}`);
    }
  };

  const handleCashCollected = (order) => {
    // In a real app, this would be an API call
    alert(
      `Cash collected for Order #${
        order.orderId?.split("-")[1] || order.id || "N/A"
      }!`
    );
    const updatedOrders = orders.map((o) => {
      if (o.id === order.id) {
        return {
          ...o,
          isCashCollected: true,
          cashCollectedBy: agent.id,
          cashCollectedAt: new Date().toISOString(),
        };
      }
      return o;
    });
    setOrders(updatedOrders);
  };

  const getActionButton = (order) => {
    switch (order.status) {
      case "available":
        return (
          <Button
            variant='primary'
            size='small'
            onClick={() => handleAcceptOrder(order)}
          >
            Accept Order
          </Button>
        );
      case "assigned":
        return (
          <Button
            variant='primary'
            size='small'
            onClick={() => handlePickUpOrder(order)}
          >
            Pick Up
          </Button>
        );
      case "pickedUp":
        return (
          <Button
            variant='primary'
            size='small'
            onClick={() => handleDeliverOrder(order)}
          >
            Deliver
          </Button>
        );
      case "delivered":
        if (!order.isCashCollected) {
          return (
            <Button
              variant='success'
              size='small'
              onClick={() => handleCashCollected(order)}
            >
              Cash Received
            </Button>
          );
        } else {
          return (
            <Button variant='outline' size='small' disabled>
              Cash Collected
            </Button>
          );
        }
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500'></div>
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      <h1 className='text-2xl font-bold mb-6'>Delivery Orders</h1>

      {/* Filters & Search */}
      <Card className='mb-6'>
        <div className='flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4'>
          <div className='flex-1 relative'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search orders by ID, customer, or restaurant...'
              className='w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className='flex gap-2'>
            <select
              className='px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
            >
              <option value='all'>All Orders</option>
              <option value='available'>Available</option>
              <option value='active'>Active</option>
              <option value='completed'>Completed</option>
            </select>
          </div>
        </div>

        {/* Order Status Tabs */}
        <div className='flex border-b'>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "all"
                ? "text-primary-600 border-b-2 border-primary-500"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("all")}
          >
            All
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "available"
                ? "text-primary-600 border-b-2 border-primary-500"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("available")}
          >
            Available
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "active"
                ? "text-primary-600 border-b-2 border-primary-500"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("active")}
          >
            Active
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "completed"
                ? "text-primary-600 border-b-2 border-primary-500"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("completed")}
          >
            Completed
          </button>
        </div>
      </Card>

      {/* Orders List */}
      {filteredOrders.length === 0 ? (
        <Card>
          <div className='text-center py-8'>
            <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4'>
              <Package size={24} className='text-gray-400' />
            </div>
            <h3 className='text-lg font-medium mb-2'>No Orders Found</h3>
            <p className='text-gray-500 mb-4'>
              {activeTab === "available"
                ? "There are no available orders at the moment."
                : activeTab === "active"
                ? "You don't have any active orders."
                : activeTab === "completed"
                ? "You haven't completed any orders yet."
                : "No orders match your search criteria."}
            </p>
            {activeTab !== "available" && (
              <Button
                variant='primary'
                onClick={() => {
                  setActiveTab("available");
                  setSearchQuery("");
                }}
              >
                Find Available Orders
              </Button>
            )}
          </div>
        </Card>
      ) : (
        <div className='space-y-4'>
          {filteredOrders.map((order) => (
            <Card key={order.id}>
              <div
                className='cursor-pointer'
                onClick={() => toggleOrderExpand(order.id)}
              >
                <div className='flex flex-col md:flex-row md:items-center justify-between p-4'>
                  <div className='mb-4 md:mb-0'>
                    <div className='flex items-center mb-2'>
                      <h3 className='font-medium mr-3'>
                        Order #
                        {order.orderId?.split("-")[1] || order.id || "N/A"}
                      </h3>
                      {getStatusBadge(order.status)}
                    </div>
                    <div className='flex items-center text-sm text-gray-600 mb-2'>
                      <Clock size={16} className='mr-1' />
                      <span>
                        {order.status === "available"
                          ? "New order"
                          : order.status === "assigned"
                          ? `Assigned at ${formatDate(order.assignedAt)}`
                          : order.status === "pickedUp"
                          ? `Picked up at ${formatDate(order.pickedUpAt)}`
                          : `Delivered at ${formatDate(order.deliveredAt)}`}
                      </span>
                    </div>
                    <div className='flex items-center text-sm text-gray-600'>
                      <MapPin size={16} className='mr-1' />
                      <span>
                        {order.distance || "2.5"} km •{" "}
                        {order.estimatedTime ||
                          order.estimatedDeliveryTime ||
                          "30"}{" "}
                        min
                      </span>
                    </div>
                  </div>

                  <div className='flex flex-col md:flex-row items-start md:items-center'>
                    <div className='mb-4 md:mb-0 md:mr-6'>
                      <div className='text-sm text-gray-600 mb-1'>Earnings</div>
                      <div className='font-bold text-lg'>
                        {formatCurrency(
                          order.earnings || order.deliveryFee || 50
                        )}
                      </div>
                    </div>

                    <div className='flex items-center'>
                      {getActionButton(order)}
                      <button
                        className='ml-2 text-gray-400 hover:text-gray-600'
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewOrderDetails(order);
                        }}
                      >
                        {expandedOrderId === order.id ? (
                          <ChevronUp size={20} />
                        ) : (
                          <ChevronDown size={20} />
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Expanded Order Details */}
                {expandedOrderId === order.id && (
                  <div className='px-4 pb-4 pt-2 border-t mt-2'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                      <div>
                        <h4 className='font-medium mb-3'>Pickup Details</h4>
                        <div className='bg-gray-50 p-3 rounded-lg'>
                          <div className='font-medium mb-1'>
                            {order.restaurantName}
                          </div>
                          <div className='text-sm text-gray-600 mb-2'>
                            {order.restaurantLocation.address}
                          </div>
                          <div className='flex items-center text-sm text-gray-600'>
                            <Phone size={16} className='mr-1' />
                            <span>{order.restaurantPhone}</span>
                          </div>
                        </div>

                        <h4 className='font-medium mt-4 mb-3'>Order Items</h4>
                        <div className='bg-gray-50 p-3 rounded-lg'>
                          <ul className='space-y-2'>
                            {order.orderDetails.items.map((item, index) => (
                              <li
                                key={index}
                                className='flex justify-between text-sm'
                              >
                                <span>
                                  {item.quantity}x {item.name}
                                </span>
                              </li>
                            ))}
                          </ul>
                          <div className='mt-3 pt-3 border-t border-gray-200 flex justify-between font-medium'>
                            <span>Total</span>
                            <span>
                              {formatCurrency(order.orderDetails.totalAmount)}
                            </span>
                          </div>
                          <div className='mt-2 text-sm text-gray-600'>
                            Payment:{" "}
                            {order.orderDetails.paymentMethod ===
                            "cashOnDelivery"
                              ? "Cash on Delivery"
                              : order.orderDetails.paymentMethod}
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className='font-medium mb-3'>Delivery Details</h4>
                        <div className='bg-gray-50 p-3 rounded-lg'>
                          <div className='font-medium mb-1'>
                            {order.customerName}
                          </div>
                          <div className='text-sm text-gray-600 mb-2'>
                            {order.customerLocation.address}
                          </div>
                          <div className='flex items-center text-sm text-gray-600'>
                            <Phone size={16} className='mr-1' />
                            <span>{order.customerPhone}</span>
                          </div>
                        </div>

                        <div className='mt-4 flex space-x-2'>
                          <Button
                            variant='outline'
                            size='small'
                            icon={<Navigation size={16} />}
                            className='flex-1'
                            onClick={(e) => {
                              e.stopPropagation();
                              // In a real app, this would open maps with directions
                              alert("Opening navigation...");
                            }}
                          >
                            Navigate
                          </Button>
                          <Button
                            variant='outline'
                            size='small'
                            icon={<Phone size={16} />}
                            className='flex-1'
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(`tel:${order.customerPhone}`);
                            }}
                          >
                            Call
                          </Button>
                          <Button
                            variant='outline'
                            size='small'
                            icon={<MessageCircle size={16} />}
                            className='flex-1'
                            onClick={(e) => {
                              e.stopPropagation();
                              // In a real app, this would open a chat interface
                              alert("Opening chat...");
                            }}
                          >
                            Message
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-auto animate-fade-in'>
            <div className='p-6 border-b flex justify-between items-center'>
              <div>
                <h2 className='text-xl font-semibold'>
                  Order #
                  {selectedOrder.orderId?.split("-")[1] ||
                    selectedOrder.id ||
                    "N/A"}
                </h2>
                <div className='flex items-center mt-1'>
                  <Calendar size={16} className='text-gray-500 mr-1' />
                  <span className='text-sm text-gray-500'>
                    {new Date(
                      selectedOrder.assignedAt || selectedOrder.createdAt
                    ).toLocaleString()}
                  </span>
                </div>
              </div>
              <div className='flex items-center'>
                {getStatusBadge(selectedOrder.status)}
                <button
                  className='ml-4 text-gray-400 hover:text-gray-600'
                  onClick={() => setShowOrderDetails(false)}
                >
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='h-6 w-6'
                    fill='none'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M6 18L18 6M6 6l12 12'
                    />
                  </svg>
                </button>
              </div>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
                <div>
                  <h3 className='text-lg font-semibold mb-4'>
                    Pickup Information
                  </h3>
                  <Card className='mb-6'>
                    <div className='p-4'>
                      <div className='flex items-start mb-4'>
                        <div className='w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3'>
                          <Package size={20} className='text-blue-600' />
                        </div>
                        <div>
                          <h4 className='font-medium'>
                            {selectedOrder.restaurantName}
                          </h4>
                          <p className='text-sm text-gray-600 mt-1'>
                            {selectedOrder.restaurantLocation.address}
                          </p>
                          <div className='flex items-center mt-2'>
                            <Button
                              variant='outline'
                              size='small'
                              icon={<Phone size={16} />}
                              onClick={() =>
                                window.open(
                                  `tel:${selectedOrder.restaurantPhone}`
                                )
                              }
                            >
                              Call Restaurant
                            </Button>
                            <Button
                              variant='outline'
                              size='small'
                              icon={<Navigation size={16} />}
                              className='ml-2'
                              onClick={() =>
                                alert("Opening navigation to restaurant...")
                              }
                            >
                              Navigate
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>

                  <h3 className='text-lg font-semibold mb-4'>Order Details</h3>
                  <Card>
                    <div className='p-4'>
                      <div className='space-y-3 mb-4'>
                        {selectedOrder.orderDetails.items.map((item, index) => (
                          <div key={index} className='flex justify-between'>
                            <span>
                              {item.quantity}x {item.name}
                            </span>
                          </div>
                        ))}
                      </div>

                      <div className='border-t border-gray-100 pt-4 mt-4'>
                        <div className='flex justify-between font-semibold'>
                          <span>Total</span>
                          <span>
                            {formatCurrency(
                              selectedOrder.orderDetails.totalAmount
                            )}
                          </span>
                        </div>
                        <div className='flex items-center mt-2 text-sm text-gray-600'>
                          <span>Payment Method:</span>
                          <span className='ml-1 font-medium'>
                            {selectedOrder.orderDetails.paymentMethod ===
                            "cashOnDelivery"
                              ? "Cash on Delivery"
                              : selectedOrder.orderDetails.paymentMethod}
                          </span>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>

                <div>
                  <h3 className='text-lg font-semibold mb-4'>
                    Delivery Information
                  </h3>
                  <Card className='mb-6'>
                    <div className='p-4'>
                      <div className='flex items-start mb-4'>
                        <div className='w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3'>
                          <User size={20} className='text-green-600' />
                        </div>
                        <div>
                          <h4 className='font-medium'>
                            {selectedOrder.customerName}
                          </h4>
                          <p className='text-sm text-gray-600 mt-1'>
                            {selectedOrder.customerLocation.address}
                          </p>
                          <div className='flex items-center mt-2'>
                            <Button
                              variant='outline'
                              size='small'
                              icon={<Phone size={16} />}
                              onClick={() =>
                                window.open(
                                  `tel:${selectedOrder.customerPhone}`
                                )
                              }
                            >
                              Call Customer
                            </Button>
                            <Button
                              variant='outline'
                              size='small'
                              icon={<MessageCircle size={16} />}
                              className='ml-2'
                              onClick={() =>
                                alert("Opening chat with customer...")
                              }
                            >
                              Message
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>

                  <h3 className='text-lg font-semibold mb-4'>
                    Delivery Details
                  </h3>
                  <Card className='mb-6'>
                    <div className='p-4'>
                      <div className='flex items-center justify-between mb-4'>
                        <div className='flex items-center'>
                          <MapPin size={20} className='text-gray-500 mr-2' />
                          <span>Distance</span>
                        </div>
                        <span className='font-medium'>
                          {selectedOrder.distance} km
                        </span>
                      </div>

                      <div className='flex items-center justify-between mb-4'>
                        <div className='flex items-center'>
                          <Clock size={20} className='text-gray-500 mr-2' />
                          <span>Estimated Time</span>
                        </div>
                        <span className='font-medium'>
                          {selectedOrder.estimatedTime} min
                        </span>
                      </div>

                      <div className='flex items-center justify-between'>
                        <div className='flex items-center'>
                          <DollarSign
                            size={20}
                            className='text-gray-500 mr-2'
                          />
                          <span>Earnings</span>
                        </div>
                        <span className='font-medium'>
                          {formatCurrency(selectedOrder.earnings)}
                        </span>
                      </div>
                    </div>
                  </Card>

                  <div className='flex justify-between items-center pt-4 border-t'>
                    <div>
                      <p className='text-sm text-gray-500'>Payment Method</p>
                      <p className='font-medium capitalize'>
                        {selectedOrder.orderDetails.paymentMethod ===
                        "cashOnDelivery"
                          ? "Cash on Delivery"
                          : selectedOrder.orderDetails.paymentMethod}
                      </p>
                      {selectedOrder.orderDetails.paymentMethod ===
                        "cashOnDelivery" && (
                        <p className='text-sm text-gray-500 mt-1'>
                          Amount to Collect:{" "}
                          {formatCurrency(
                            selectedOrder.orderDetails.totalAmount
                          )}
                        </p>
                      )}
                    </div>

                    {selectedOrder.status === "cancelled" ? (
                      <Badge variant='danger' size='medium'>
                        Order Cancelled
                      </Badge>
                    ) : (
                      <div className='flex space-x-3'>
                        {selectedOrder.orderDetails.paymentMethod ===
                          "cashOnDelivery" &&
                          selectedOrder.status === "delivered" &&
                          !selectedOrder.isCashCollected && (
                            <Button
                              variant='success'
                              size='small'
                              onClick={() => handleCashCollected(selectedOrder)}
                            >
                              Mark Cash Collected
                            </Button>
                          )}
                        {selectedOrder.status !== "delivered" &&
                          selectedOrder.status !== "cancelled" && (
                            <Button
                              variant='danger'
                              size='small'
                              icon={<XCircle size={16} />}
                              onClick={() =>
                                handleUpdateOrderStatus(
                                  selectedOrder.id,
                                  "cancelled"
                                )
                              }
                            >
                              Cancel Order
                            </Button>
                          )}
                        <Button
                          variant='primary'
                          onClick={() => setShowOrderDetails(false)}
                        >
                          Close
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DeliveryOrders;
