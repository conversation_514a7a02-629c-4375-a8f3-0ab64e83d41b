import React, { useState, useEffect } from "react";
import {
  TruckIcon,
  MapPin,
  Clock,
  DollarSign,
  Star,
  Package,
  Activity,
  Users,
  Navigation,
  Play,
  Square,
  RefreshCw,
  Bell,
  Settings,
  Eye,
  CheckCircle,
  AlertCircle,
  Zap,
  Target,
  TrendingUp,
  Calendar,
  Timer,
  Route,
  Award,
  LogIn,
  LogOut,
  Coffee,
  UserCheck,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";

function DeliveryDashboard() {
  const { user } = useAuth();

  // State management
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isOnline, setIsOnline] = useState(false);
  const [currentShift, setCurrentShift] = useState(null);
  const [locationTracking, setLocationTracking] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Employee-specific state
  const [isClocked, setIsClocked] = useState(false);
  const [shiftStartTime, setShiftStartTime] = useState(null);
  const [currentShiftHours, setCurrentShiftHours] = useState(0);
  const [employeeStatus, setEmployeeStatus] = useState("offline");
  const [clockLoading, setClockLoading] = useState(false);

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await deliveryAgentApi.getDashboard();

      if (result.success) {
        setDashboardData(result.data);
        setIsOnline(result.data.agent_info?.is_online || false);
        setCurrentShift(result.data.current_shift);

        // Update employee-specific state
        setIsClocked(result.data.agent_info?.is_clocked_in || false);
        setShiftStartTime(result.data.agent_info?.last_clock_in);
        setCurrentShiftHours(result.data.agent_info?.current_shift_hours || 0);
        setEmployeeStatus(result.data.agent_info?.availability || "offline");
      } else {
        setError(result.error?.message || "Failed to load dashboard data");
      }
    } catch (err) {
      console.error("Dashboard load error:", err);
      setError("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  // Refresh dashboard data
  const refreshDashboard = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  // Employee Clock In/Out Functions
  const handleClockIn = async () => {
    try {
      setClockLoading(true);
      const result = await deliveryAgentApi.clockIn();

      if (result.success) {
        setIsClocked(true);
        setShiftStartTime(result.data.clock_in_time);
        setEmployeeStatus(result.data.availability);
        await refreshDashboard();
      } else {
        setError(result.error?.message || "Failed to clock in");
      }
    } catch (err) {
      console.error("Clock in error:", err);
      setError("Failed to clock in");
    } finally {
      setClockLoading(false);
    }
  };

  const handleClockOut = async () => {
    try {
      setClockLoading(true);
      const result = await deliveryAgentApi.clockOut();

      if (result.success) {
        setIsClocked(false);
        setShiftStartTime(null);
        setCurrentShiftHours(result.data.shift_hours);
        setEmployeeStatus("offline");
        await refreshDashboard();
      } else {
        setError(result.error?.message || "Failed to clock out");
      }
    } catch (err) {
      console.error("Clock out error:", err);
      setError("Failed to clock out");
    } finally {
      setClockLoading(false);
    }
  };

  const handleSetBreak = async () => {
    try {
      const result = await deliveryAgentApi.setBreak();

      if (result.success) {
        setEmployeeStatus("break");
        await refreshDashboard();
      } else {
        setError(result.error?.message || "Failed to set break");
      }
    } catch (err) {
      console.error("Set break error:", err);
      setError("Failed to set break");
    }
  };

  const handleReturnFromBreak = async () => {
    try {
      const result = await deliveryAgentApi.returnFromBreak();

      if (result.success) {
        setEmployeeStatus("available");
        await refreshDashboard();
      } else {
        setError(result.error?.message || "Failed to return from break");
      }
    } catch (err) {
      console.error("Return from break error:", err);
      setError("Failed to return from break");
    }
  };

  // Toggle online status (Legacy - for compatibility)
  const toggleOnlineStatus = async () => {
    try {
      const newStatus = !isOnline;
      const result = await deliveryAgentApi.updateAvailability({
        is_online: newStatus,
        availability: newStatus ? "available" : "offline",
      });

      if (result.success) {
        setIsOnline(newStatus);
        await refreshDashboard();
      } else {
        setError(result.error?.message || "Failed to update status");
      }
    } catch (err) {
      console.error("Status update error:", err);
      setError("Failed to update status");
    }
  };

  // Start shift
  const startShift = async () => {
    try {
      const location = await deliveryAgentApi.getCurrentLocation();
      const result = await deliveryAgentApi.startShift({
        latitude: location.latitude,
        longitude: location.longitude,
        planned_duration_hours: 8.0,
      });

      if (result.success) {
        await refreshDashboard();
        // Start location tracking
        const tracking = deliveryAgentApi.startLocationTracking();
        setLocationTracking(tracking);
      } else {
        setError(result.error?.message || "Failed to start shift");
      }
    } catch (err) {
      console.error("Start shift error:", err);
      setError("Failed to start shift");
    }
  };

  // End shift
  const endShift = async () => {
    try {
      const location = await deliveryAgentApi.getCurrentLocation();
      const result = await deliveryAgentApi.endShift({
        latitude: location.latitude,
        longitude: location.longitude,
        notes: "Shift ended from dashboard",
      });

      if (result.success) {
        await refreshDashboard();
        // Stop location tracking
        if (locationTracking) {
          deliveryAgentApi.stopLocationTracking(locationTracking);
          setLocationTracking(null);
        }
      } else {
        setError(result.error?.message || "Failed to end shift");
      }
    } catch (err) {
      console.error("End shift error:", err);
      setError("Failed to end shift");
    }
  };

  useEffect(() => {
    loadDashboardData();

    // Cleanup location tracking on unmount
    return () => {
      if (locationTracking) {
        deliveryAgentApi.stopLocationTracking(locationTracking);
      }
    };
  }, [user]);

  // Error state
  if (error) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <h2 className='text-xl font-semibold text-red-600 mb-2'>
            Error Loading Dashboard
          </h2>
          <p className='text-gray-600 mb-4'>{error}</p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  // Authentication check
  if (!user) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <h2 className='text-xl font-semibold text-gray-900 mb-2'>
            Authentication Required
          </h2>
          <p className='text-gray-600'>
            Please log in to access the delivery dashboard.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4'></div>
          <p className='text-gray-600'>Loading delivery dashboard...</p>
        </div>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className='min-h-screen bg-gradient-to-br from-primary-50 via-orange-50 to-primary-100 flex items-center justify-center p-6'>
        <Card className='p-8 text-center bg-white shadow-xl border-0 rounded-2xl max-w-md'>
          <div className='w-20 h-20 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-6'>
            <TruckIcon className='h-10 w-10 text-white' />
          </div>
          <h2 className='text-2xl font-bold bg-gradient-to-r from-primary-500 to-primary-600 bg-clip-text text-transparent mb-3'>
            Agent Profile Not Found
          </h2>
          <p className='text-gray-600 text-lg mb-6'>
            Unable to load delivery agent profile. Please contact support.
          </p>
          <Button
            onClick={() => window.location.reload()}
            className='group px-8 py-4 bg-gradient-to-r from-primary-500 via-primary-600 to-orange-500 hover:from-primary-600 hover:via-orange-500 hover:to-orange-600 text-white rounded-xl shadow-2xl hover:shadow-primary-500/25 transition-all duration-300 transform hover:scale-110 font-bold text-lg relative overflow-hidden'
          >
            <div className='absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700'></div>
            <span className='relative flex items-center space-x-2'>
              <RefreshCw className='h-5 w-5 group-hover:animate-spin transition-transform duration-300' />
              <span>Retry Connection</span>
            </span>
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className='p-6 space-y-8'>
      {/* Beautiful Agent Status Header */}
      <Card className='p-8 bg-gradient-to-br from-white via-primary-50/30 to-orange-50/50 shadow-2xl border-0 rounded-3xl backdrop-blur-sm relative overflow-hidden'>
        {/* Decorative background elements */}
        <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-200/20 to-orange-200/20 rounded-full -translate-y-16 translate-x-16'></div>
        <div className='absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-accent-green/10 to-primary-200/10 rounded-full translate-y-12 -translate-x-12'></div>

        <div className='relative z-10'>
          <div className='flex items-center justify-between mb-8'>
            <div className='flex items-center space-x-6'>
              <div className='relative'>
                <div className='w-24 h-24 rounded-full bg-gradient-to-r from-primary-500 via-primary-600 to-orange-500 flex items-center justify-center shadow-2xl transform hover:scale-105 transition-transform duration-300'>
                  <TruckIcon size={44} className='text-white' />
                </div>
                <div className='absolute -bottom-1 -right-1 w-8 h-8 bg-accent-green rounded-full flex items-center justify-center shadow-lg'>
                  <div className='w-3 h-3 bg-white rounded-full animate-pulse'></div>
                </div>
              </div>
              <div>
                <div className='flex items-center space-x-3 mb-2'>
                  <h1 className='text-4xl font-bold bg-gradient-to-r from-primary-600 via-primary-500 to-orange-500 bg-clip-text text-transparent'>
                    {dashboardData?.agent_info?.full_name ||
                      user?.name ||
                      "Delivery Agent"}
                  </h1>
                  <div
                    className={`px-3 py-1 text-white text-sm font-semibold rounded-full shadow-lg ${
                      dashboardData?.agent_info?.employment_status === "active"
                        ? "bg-gradient-to-r from-accent-green to-accent-green-dark"
                        : "bg-gradient-to-r from-gray-400 to-gray-500"
                    }`}
                  >
                    {dashboardData?.agent_info?.employment_status?.toUpperCase() ||
                      "UNKNOWN"}
                  </div>
                </div>
                <p className='text-gray-600 text-lg font-medium mb-3'>
                  Agent ID:{" "}
                  <span className='text-primary-600 font-bold'>
                    {dashboardData?.agent_info?.agent_id || "N/A"}
                  </span>
                </p>
                <div className='flex items-center space-x-4'>
                  <div
                    className={`flex items-center px-3 py-1 rounded-full ${
                      dashboardData?.agent_info?.availability === "available"
                        ? "bg-accent-green/10"
                        : dashboardData?.agent_info?.availability === "busy"
                        ? "bg-orange-100"
                        : "bg-gray-100"
                    }`}
                  >
                    <div
                      className={`w-2 h-2 rounded-full mr-2 ${
                        dashboardData?.agent_info?.availability === "available"
                          ? "bg-accent-green animate-pulse"
                          : dashboardData?.agent_info?.availability === "busy"
                          ? "bg-orange-500 animate-pulse"
                          : "bg-gray-400"
                      }`}
                    ></div>
                    <span
                      className={`font-semibold text-sm ${
                        dashboardData?.agent_info?.availability === "available"
                          ? "text-accent-green-dark"
                          : dashboardData?.agent_info?.availability === "busy"
                          ? "text-orange-700"
                          : "text-gray-600"
                      }`}
                    >
                      {dashboardData?.agent_info?.availability === "available"
                        ? "Online & Ready"
                        : dashboardData?.agent_info?.availability === "busy"
                        ? "Busy on Delivery"
                        : dashboardData?.agent_info?.availability === "break"
                        ? "On Break"
                        : "Offline"}
                    </span>
                  </div>
                  <div className='flex items-center bg-blue-50 px-3 py-1 rounded-full'>
                    <Clock className='h-4 w-4 text-blue-600 mr-1' />
                    <span className='text-blue-700 font-semibold text-sm'>
                      {dashboardData?.agent_info?.is_clocked_in
                        ? `${
                            dashboardData?.today_stats?.hours_worked || 0
                          }h Active`
                        : "Not Clocked In"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className='text-right bg-white/60 backdrop-blur-sm p-4 rounded-2xl shadow-lg'>
              <p className='text-sm text-gray-500 uppercase tracking-wide mb-1'>
                Today's Rating
              </p>
              <div className='flex items-center justify-end space-x-2 mb-2'>
                <div className='flex space-x-1'>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className='h-4 w-4 text-yellow-400 fill-current'
                    />
                  ))}
                </div>
              </div>
              <span className='text-3xl font-bold bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent'>
                {dashboardData?.agent_info?.rating || "0.0"}
              </span>
              <p className='text-xs text-gray-500 mt-1'>
                {dashboardData?.agent_info?.rating >= 4.5
                  ? "Excellent Performance"
                  : dashboardData?.agent_info?.rating >= 4.0
                  ? "Good Performance"
                  : dashboardData?.agent_info?.rating >= 3.0
                  ? "Average Performance"
                  : "Needs Improvement"}
              </p>
            </div>
          </div>
        </div>

        {/* Next Action Recommendation */}
        {dashboardData?.status_info?.next_action && (
          <div className='mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-4 shadow-lg'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-blue-500 rounded-full'>
                <Activity className='h-5 w-5 text-white' />
              </div>
              <div>
                <h3 className='text-lg font-semibold text-blue-900'>
                  Next Action
                </h3>
                <p className='text-blue-700 font-medium'>
                  {dashboardData.status_info.next_action}
                </p>
              </div>
              {dashboardData?.status_info?.can_accept_orders && (
                <div className='ml-auto'>
                  <div className='px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium'>
                    Ready for Orders
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Beautiful Statistics Cards */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          {/* Active Orders Card */}
          <div className='group relative bg-gradient-to-br from-white via-primary-50/50 to-orange-50 p-6 rounded-2xl border border-primary-200/50 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer'>
            <div className='absolute inset-0 bg-gradient-to-br from-primary-500/5 to-orange-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>
            <div className='relative z-10'>
              <div className='flex items-center justify-between mb-4'>
                <div className='p-4 bg-gradient-to-br from-primary-500 to-orange-500 rounded-2xl shadow-lg group-hover:scale-110 transition-transform duration-300'>
                  <Activity className='h-7 w-7 text-white' />
                </div>
                <div className='text-right'>
                  <span className='text-4xl font-bold bg-gradient-to-r from-primary-600 to-orange-500 bg-clip-text text-transparent'>
                    {activeOrders.length}
                  </span>
                  <div className='w-8 h-1 bg-gradient-to-r from-primary-500 to-orange-500 rounded-full mt-1 ml-auto'></div>
                </div>
              </div>
              <div>
                <p className='text-sm text-gray-500 uppercase tracking-wider font-semibold mb-1'>
                  Active Orders
                </p>
                <p className='text-primary-700 font-bold text-lg'>
                  In Progress
                </p>
                <div className='flex items-center mt-2 text-xs text-gray-500'>
                  <TrendingUp className='h-3 w-3 mr-1' />
                  <span>+12% from yesterday</span>
                </div>
              </div>
            </div>
          </div>

          {/* Completed Orders Card */}
          <div className='group relative bg-gradient-to-br from-white via-accent-green/10 to-green-50 p-6 rounded-2xl border border-accent-green/30 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer'>
            <div className='absolute inset-0 bg-gradient-to-br from-accent-green/5 to-green-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>
            <div className='relative z-10'>
              <div className='flex items-center justify-between mb-4'>
                <div className='p-4 bg-gradient-to-br from-accent-green to-accent-green-dark rounded-2xl shadow-lg group-hover:scale-110 transition-transform duration-300'>
                  <CheckCircle className='h-7 w-7 text-white' />
                </div>
                <div className='text-right'>
                  <span className='text-4xl font-bold bg-gradient-to-r from-accent-green to-accent-green-dark bg-clip-text text-transparent'>
                    {completedOrders.length}
                  </span>
                  <div className='w-8 h-1 bg-gradient-to-r from-accent-green to-accent-green-dark rounded-full mt-1 ml-auto'></div>
                </div>
              </div>
              <div>
                <p className='text-sm text-gray-500 uppercase tracking-wider font-semibold mb-1'>
                  Completed Today
                </p>
                <p className='text-accent-green-dark font-bold text-lg'>
                  Delivered
                </p>
                <div className='flex items-center mt-2 text-xs text-gray-500'>
                  <Award className='h-3 w-3 mr-1' />
                  <span>Great performance!</span>
                </div>
              </div>
            </div>
          </div>

          {/* Available Orders Card */}
          <div className='group relative bg-gradient-to-br from-white via-blue-50/50 to-indigo-50 p-6 rounded-2xl border border-blue-200/50 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer'>
            <div className='absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>
            <div className='relative z-10'>
              <div className='flex items-center justify-between mb-4'>
                <div className='p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg group-hover:scale-110 transition-transform duration-300'>
                  <Package className='h-7 w-7 text-white' />
                </div>
                <div className='text-right'>
                  <span className='text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent'>
                    {availableOrders.length}
                  </span>
                  <div className='w-8 h-1 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mt-1 ml-auto'></div>
                </div>
              </div>
              <div>
                <p className='text-sm text-gray-500 uppercase tracking-wider font-semibold mb-1'>
                  Available Orders
                </p>
                <p className='text-blue-700 font-bold text-lg'>
                  Ready to Accept
                </p>
                <div className='flex items-center mt-2 text-xs text-gray-500'>
                  <Target className='h-3 w-3 mr-1' />
                  <span>New orders waiting</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Beautiful Active Orders Section */}
      {activeOrders.length > 0 && (
        <Card className='p-8 bg-gradient-to-br from-white via-gray-50/30 to-primary-50/20 shadow-2xl border-0 rounded-3xl relative overflow-hidden'>
          {/* Decorative elements */}
          <div className='absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-primary-200/10 to-orange-200/10 rounded-full -translate-y-20 translate-x-20'></div>

          <div className='relative z-10'>
            <div className='flex items-center justify-between mb-8'>
              <div className='flex items-center space-x-4'>
                <div className='p-3 bg-gradient-to-r from-primary-500 to-orange-500 rounded-2xl shadow-lg'>
                  <Activity className='h-8 w-8 text-white' />
                </div>
                <div>
                  <h2 className='text-3xl font-bold bg-gradient-to-r from-primary-600 to-orange-500 bg-clip-text text-transparent'>
                    Active Orders
                  </h2>
                  <p className='text-gray-600'>Orders currently in progress</p>
                </div>
              </div>
              <div className='flex items-center space-x-3'>
                <div className='px-4 py-2 bg-gradient-to-r from-primary-500 to-orange-500 text-white rounded-full text-sm font-bold shadow-lg'>
                  {activeOrders.length} Active
                </div>
                <Button className='group px-6 py-3 bg-white border-2 border-primary-200 text-primary-600 hover:bg-gradient-to-r hover:from-primary-500 hover:to-orange-500 hover:text-white hover:border-transparent rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-semibold'>
                  <span className='flex items-center space-x-2'>
                    <Eye className='h-4 w-4 transition-transform duration-300 group-hover:scale-110' />
                    <span>View All</span>
                  </span>
                </Button>
              </div>
            </div>

            <div className='space-y-6'>
              {activeOrders.map((order, index) => (
                <Card
                  key={order.id}
                  className='group p-6 bg-gradient-to-r from-white via-white to-primary-50/30 border-l-4 border-l-primary-500 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 rounded-2xl relative overflow-hidden'
                >
                  {/* Order number badge */}
                  <div className='absolute top-4 right-4 w-8 h-8 bg-gradient-to-r from-primary-500 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm'>
                    {index + 1}
                  </div>

                  <div className='flex justify-between items-start'>
                    <div className='flex-1 pr-6'>
                      <div className='flex items-center space-x-4 mb-4'>
                        <div className='relative'>
                          <div className='w-14 h-14 bg-gradient-to-br from-primary-100 to-orange-100 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300'>
                            <Users className='h-7 w-7 text-primary-600' />
                          </div>
                          <div className='absolute -bottom-1 -right-1 w-5 h-5 bg-accent-green rounded-full flex items-center justify-center'>
                            <div className='w-2 h-2 bg-white rounded-full'></div>
                          </div>
                        </div>
                        <div>
                          <h3 className='text-xl font-bold text-gray-900 mb-1'>
                            {order.customerName}
                          </h3>
                          <p className='text-primary-600 font-bold text-lg'>
                            {order.restaurantName}
                          </p>
                          <div className='flex items-center mt-1 text-xs text-gray-500'>
                            <Timer className='h-3 w-3 mr-1' />
                            <span>Estimated: 25 mins</span>
                          </div>
                        </div>
                      </div>

                      <div className='bg-gradient-to-r from-gray-50 to-primary-50/50 p-4 rounded-xl border border-gray-200/50'>
                        <div className='flex items-start space-x-3'>
                          <div className='p-2 bg-primary-100 rounded-lg'>
                            <MapPin className='h-5 w-5 text-primary-600' />
                          </div>
                          <div className='flex-1'>
                            <p className='text-xs text-gray-500 uppercase tracking-wide mb-1'>
                              Delivery Address
                            </p>
                            <p className='text-gray-900 font-semibold'>
                              {order.deliveryAddress}
                            </p>
                            <div className='flex items-center mt-2 space-x-4'>
                              <div className='flex items-center text-xs text-gray-500'>
                                <Route className='h-3 w-3 mr-1' />
                                <span>2.3 km away</span>
                              </div>
                              <div className='flex items-center text-xs text-gray-500'>
                                <Navigation className='h-3 w-3 mr-1' />
                                <span>8 min drive</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className='text-right'>
                      <div className='mb-4'>
                        <p className='text-sm text-gray-500 mb-1'>
                          Order Value
                        </p>
                        <div className='text-3xl font-bold bg-gradient-to-r from-accent-green to-accent-green-dark bg-clip-text text-transparent'>
                          ${order.orderTotal}
                        </div>
                        <p className='text-xs text-gray-500'>
                          + $4.50 delivery fee
                        </p>
                      </div>

                      <div className='mb-4'>
                        <div className='px-4 py-2 bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 rounded-xl text-sm font-bold border border-yellow-200'>
                          {order.status}
                        </div>
                      </div>

                      <div className='space-y-3'>
                        <Button className='group w-full px-4 py-3 bg-gradient-to-r from-primary-500 via-primary-600 to-orange-500 hover:from-primary-600 hover:via-orange-500 hover:to-orange-600 text-white rounded-xl font-bold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 relative overflow-hidden'>
                          <div className='absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700'></div>
                          <span className='relative flex items-center justify-center space-x-2'>
                            <Eye className='h-5 w-5 transition-transform duration-300 group-hover:scale-110' />
                            <span>View Details</span>
                          </span>
                        </Button>
                        <Button className='group w-full px-4 py-3 bg-white border-2 border-accent-green text-accent-green-dark hover:bg-gradient-to-r hover:from-accent-green hover:to-accent-green-dark hover:text-white hover:border-transparent rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 relative overflow-hidden'>
                          <div className='absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700'></div>
                          <span className='relative flex items-center justify-center space-x-2'>
                            <Navigation className='h-5 w-5 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12' />
                            <span>Navigate</span>
                          </span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </Card>
      )}

      {/* Beautiful Quick Actions Section */}
      <Card className='p-8 bg-gradient-to-br from-white via-gray-50/30 to-blue-50/20 shadow-2xl border-0 rounded-3xl relative overflow-hidden'>
        {/* Decorative background */}
        <div className='absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-blue-200/10 to-purple-200/10 rounded-full -translate-y-16 -translate-x-16'></div>

        <div className='relative z-10'>
          <div className='text-center mb-8'>
            <div className='inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary-500 to-orange-500 rounded-2xl shadow-lg mb-4'>
              <Zap className='h-8 w-8 text-white' />
            </div>
            <h2 className='text-3xl font-bold bg-gradient-to-r from-primary-600 to-orange-500 bg-clip-text text-transparent mb-2'>
              Quick Actions
            </h2>
            <p className='text-gray-600'>
              Access your most used features instantly
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
            {/* View Available Orders */}
            <div className='group relative'>
              <div className='absolute inset-0 bg-gradient-to-r from-accent-green to-accent-green-dark rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500'></div>
              <div className='absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>
              <Button className='relative w-full p-8 bg-gradient-to-br from-accent-green via-accent-green to-accent-green-dark hover:from-accent-green-dark hover:via-green-600 hover:to-accent-green text-white rounded-2xl flex flex-col items-center space-y-4 shadow-2xl hover:shadow-accent-green/25 transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 group overflow-hidden'>
                <div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000'></div>
                <div className='relative p-4 bg-white/20 backdrop-blur-sm rounded-2xl group-hover:scale-125 group-hover:rotate-6 transition-all duration-500 shadow-lg'>
                  <Package className='h-10 w-10 group-hover:animate-bounce' />
                </div>
                <div className='relative text-center'>
                  <span className='font-bold text-xl tracking-wide'>
                    Available Orders
                  </span>
                  <p className='text-sm opacity-90 mt-2 font-medium'>
                    Find new delivery opportunities
                  </p>
                </div>
                <div className='absolute top-4 right-4 w-8 h-8 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300'>
                  <span className='text-sm font-bold'>12</span>
                </div>
                <div className='absolute bottom-2 left-2 w-2 h-2 bg-white/40 rounded-full animate-pulse'></div>
                <div
                  className='absolute bottom-4 left-4 w-1 h-1 bg-white/60 rounded-full animate-pulse'
                  style={{ animationDelay: "0.5s" }}
                ></div>
              </Button>
            </div>

            {/* View Earnings */}
            <div className='group relative'>
              <div className='absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500'></div>
              <div className='absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>
              <Button className='relative w-full p-8 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 text-white rounded-2xl flex flex-col items-center space-y-4 shadow-2xl hover:shadow-blue-500/25 transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 group overflow-hidden'>
                <div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000'></div>
                <div className='relative p-4 bg-white/20 backdrop-blur-sm rounded-2xl group-hover:scale-125 group-hover:-rotate-6 transition-all duration-500 shadow-lg'>
                  <DollarSign className='h-10 w-10 group-hover:animate-pulse' />
                </div>
                <div className='relative text-center'>
                  <span className='font-bold text-xl tracking-wide'>
                    View Earnings
                  </span>
                  <p className='text-sm opacity-90 mt-2 font-medium'>
                    Track your daily income
                  </p>
                </div>
                <div className='absolute top-4 right-4 px-3 py-1 bg-white/30 backdrop-blur-sm rounded-full shadow-lg group-hover:scale-110 transition-transform duration-300'>
                  <span className='text-sm font-bold'>$127</span>
                </div>
                <div className='absolute top-2 left-2 w-2 h-2 bg-yellow-300/60 rounded-full animate-ping'></div>
                <div
                  className='absolute top-6 left-6 w-1 h-1 bg-yellow-400/80 rounded-full animate-ping'
                  style={{ animationDelay: "0.3s" }}
                ></div>
              </Button>
            </div>

            {/* Profile Settings */}
            <div className='group relative'>
              <div className='absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500'></div>
              <div className='absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>
              <Button className='relative w-full p-8 bg-gradient-to-br from-purple-500 via-purple-600 to-pink-600 hover:from-purple-600 hover:via-pink-600 hover:to-rose-600 text-white rounded-2xl flex flex-col items-center space-y-4 shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 group overflow-hidden'>
                <div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000'></div>
                <div className='relative p-4 bg-white/20 backdrop-blur-sm rounded-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 shadow-lg'>
                  <Settings
                    className='h-10 w-10 group-hover:animate-spin'
                    style={{ animationDuration: "2s" }}
                  />
                </div>
                <div className='relative text-center'>
                  <span className='font-bold text-xl tracking-wide'>
                    Profile Settings
                  </span>
                  <p className='text-sm opacity-90 mt-2 font-medium'>
                    Manage your account
                  </p>
                </div>
                <div className='absolute top-4 right-4 w-4 h-4 bg-yellow-400/80 backdrop-blur-sm rounded-full animate-pulse shadow-lg flex items-center justify-center'>
                  <div className='w-2 h-2 bg-yellow-200 rounded-full'></div>
                </div>
                <div className='absolute bottom-2 right-2 w-2 h-2 bg-pink-300/60 rounded-full animate-bounce'></div>
                <div
                  className='absolute bottom-4 right-6 w-1 h-1 bg-pink-400/80 rounded-full animate-bounce'
                  style={{ animationDelay: "0.2s" }}
                ></div>
              </Button>
            </div>
          </div>

          {/* Enhanced Additional Quick Links */}
          <div className='mt-10 pt-8 border-t border-gradient-to-r from-transparent via-gray-300 to-transparent'>
            <div className='text-center mb-6'>
              <h3 className='text-lg font-bold text-gray-700 mb-2'>
                Quick Access
              </h3>
              <p className='text-sm text-gray-500'>
                Frequently used tools and features
              </p>
            </div>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <Button className='group relative p-4 bg-white border-2 border-gray-200 hover:border-primary-400 hover:bg-gradient-to-br hover:from-primary-50 hover:to-orange-50 rounded-xl flex items-center space-x-3 transition-all duration-300 transform hover:scale-105 hover:shadow-lg overflow-hidden'>
                <div className='absolute inset-0 bg-gradient-to-r from-primary-500/0 to-orange-500/0 group-hover:from-primary-500/5 group-hover:to-orange-500/5 transition-all duration-300'></div>
                <div className='relative p-2 bg-gray-100 group-hover:bg-primary-100 rounded-lg transition-all duration-300 group-hover:scale-110'>
                  <Bell className='h-5 w-5 text-gray-600 group-hover:text-primary-600 transition-colors duration-300' />
                </div>
                <span className='relative text-sm font-bold text-gray-700 group-hover:text-primary-700 transition-colors duration-300'>
                  Notifications
                </span>
                <div className='absolute top-2 right-2 w-2 h-2 bg-red-400 rounded-full animate-pulse'></div>
              </Button>

              <Button className='group relative p-4 bg-white border-2 border-gray-200 hover:border-blue-400 hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50 rounded-xl flex items-center space-x-3 transition-all duration-300 transform hover:scale-105 hover:shadow-lg overflow-hidden'>
                <div className='absolute inset-0 bg-gradient-to-r from-blue-500/0 to-indigo-500/0 group-hover:from-blue-500/5 group-hover:to-indigo-500/5 transition-all duration-300'></div>
                <div className='relative p-2 bg-gray-100 group-hover:bg-blue-100 rounded-lg transition-all duration-300 group-hover:scale-110'>
                  <Calendar className='h-5 w-5 text-gray-600 group-hover:text-blue-600 transition-colors duration-300' />
                </div>
                <span className='relative text-sm font-bold text-gray-700 group-hover:text-blue-700 transition-colors duration-300'>
                  Schedule
                </span>
              </Button>

              <Button className='group relative p-4 bg-white border-2 border-gray-200 hover:border-green-400 hover:bg-gradient-to-br hover:from-green-50 hover:to-emerald-50 rounded-xl flex items-center space-x-3 transition-all duration-300 transform hover:scale-105 hover:shadow-lg overflow-hidden'>
                <div className='absolute inset-0 bg-gradient-to-r from-green-500/0 to-emerald-500/0 group-hover:from-green-500/5 group-hover:to-emerald-500/5 transition-all duration-300'></div>
                <div className='relative p-2 bg-gray-100 group-hover:bg-green-100 rounded-lg transition-all duration-300 group-hover:scale-110 group-hover:rotate-180'>
                  <RefreshCw className='h-5 w-5 text-gray-600 group-hover:text-green-600 transition-colors duration-300' />
                </div>
                <span className='relative text-sm font-bold text-gray-700 group-hover:text-green-700 transition-colors duration-300'>
                  Refresh
                </span>
              </Button>

              <Button className='group relative p-4 bg-white border-2 border-gray-200 hover:border-purple-400 hover:bg-gradient-to-br hover:from-purple-50 hover:to-pink-50 rounded-xl flex items-center space-x-3 transition-all duration-300 transform hover:scale-105 hover:shadow-lg overflow-hidden'>
                <div className='absolute inset-0 bg-gradient-to-r from-purple-500/0 to-pink-500/0 group-hover:from-purple-500/5 group-hover:to-pink-500/5 transition-all duration-300'></div>
                <div className='relative p-2 bg-gray-100 group-hover:bg-purple-100 rounded-lg transition-all duration-300 group-hover:scale-110'>
                  <Eye className='h-5 w-5 text-gray-600 group-hover:text-purple-600 transition-colors duration-300' />
                </div>
                <span className='relative text-sm font-bold text-gray-700 group-hover:text-purple-700 transition-colors duration-300'>
                  History
                </span>
              </Button>
            </div>
          </div>
        </div>
      </Card>

      {/* Beautiful System Information */}
      <Card className='p-8 bg-gradient-to-br from-white via-gray-50/30 to-indigo-50/20 shadow-2xl border-0 rounded-3xl relative overflow-hidden'>
        {/* Decorative background */}
        <div className='absolute bottom-0 right-0 w-36 h-36 bg-gradient-to-tl from-indigo-200/10 to-purple-200/10 rounded-full translate-y-18 translate-x-18'></div>

        <div className='relative z-10'>
          <div className='flex items-center justify-between mb-8'>
            <div className='flex items-center space-x-4'>
              <div className='p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl shadow-lg'>
                <AlertCircle className='h-8 w-8 text-white' />
              </div>
              <div>
                <h3 className='text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent'>
                  System Information
                </h3>
                <p className='text-gray-600'>
                  Real-time system status and metrics
                </p>
              </div>
            </div>
            <div className='px-4 py-2 bg-gradient-to-r from-accent-green to-accent-green-dark text-white rounded-full text-sm font-bold shadow-lg'>
              All Systems Online
            </div>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {/* User ID */}
            <div className='group bg-gradient-to-br from-white to-blue-50/50 p-6 rounded-2xl border border-blue-200/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
              <div className='flex items-center justify-between mb-3'>
                <div className='p-2 bg-blue-100 rounded-lg group-hover:scale-110 transition-transform duration-300'>
                  <User className='h-5 w-5 text-blue-600' />
                </div>
                <div className='w-2 h-2 bg-blue-500 rounded-full animate-pulse'></div>
              </div>
              <p className='text-xs text-gray-500 uppercase tracking-wider font-semibold mb-2'>
                User ID
              </p>
              <p className='text-xl font-bold text-gray-900'>{user?.id}</p>
            </div>

            {/* Agent Status */}
            <div className='group bg-gradient-to-br from-white to-accent-green/10 p-6 rounded-2xl border border-accent-green/30 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
              <div className='flex items-center justify-between mb-3'>
                <div className='p-2 bg-accent-green/20 rounded-lg group-hover:scale-110 transition-transform duration-300'>
                  <CheckCircle className='h-5 w-5 text-accent-green-dark' />
                </div>
                <div className='w-2 h-2 bg-accent-green rounded-full animate-pulse'></div>
              </div>
              <p className='text-xs text-gray-500 uppercase tracking-wider font-semibold mb-2'>
                Agent Status
              </p>
              <p className='text-xl font-bold text-accent-green-dark'>
                {dashboardData?.agent_info?.employment_status === "active"
                  ? "Active Employee"
                  : dashboardData?.agent_info?.application_status ||
                    "Not Found"}
              </p>
            </div>

            {/* Active Orders */}
            <div className='group bg-gradient-to-br from-white to-primary-50/50 p-6 rounded-2xl border border-primary-200/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
              <div className='flex items-center justify-between mb-3'>
                <div className='p-2 bg-primary-100 rounded-lg group-hover:scale-110 transition-transform duration-300'>
                  <Activity className='h-5 w-5 text-primary-600' />
                </div>
                <div className='px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-bold'>
                  LIVE
                </div>
              </div>
              <p className='text-xs text-gray-500 uppercase tracking-wider font-semibold mb-2'>
                Active Orders
              </p>
              <p className='text-xl font-bold text-primary-600'>
                {activeOrders.length}
              </p>
            </div>

            {/* Available Orders */}
            <div className='group bg-gradient-to-br from-white to-indigo-50/50 p-6 rounded-2xl border border-indigo-200/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
              <div className='flex items-center justify-between mb-3'>
                <div className='p-2 bg-indigo-100 rounded-lg group-hover:scale-110 transition-transform duration-300'>
                  <Package className='h-5 w-5 text-indigo-600' />
                </div>
                <div className='w-2 h-2 bg-indigo-500 rounded-full animate-pulse'></div>
              </div>
              <p className='text-xs text-gray-500 uppercase tracking-wider font-semibold mb-2'>
                Available Orders
              </p>
              <p className='text-xl font-bold text-indigo-600'>
                {availableOrders.length}
              </p>
            </div>

            {/* Today's Earnings */}
            <div className='group bg-gradient-to-br from-white to-yellow-50/50 p-6 rounded-2xl border border-yellow-200/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
              <div className='flex items-center justify-between mb-3'>
                <div className='p-2 bg-yellow-100 rounded-lg group-hover:scale-110 transition-transform duration-300'>
                  <DollarSign className='h-5 w-5 text-yellow-600' />
                </div>
                <div className='w-2 h-2 bg-yellow-500 rounded-full animate-pulse'></div>
              </div>
              <p className='text-xs text-gray-500 uppercase tracking-wider font-semibold mb-2'>
                Today's Earnings
              </p>
              <p className='text-xl font-bold text-yellow-600'>
                {todayEarnings ? "$127.50" : "$0.00"}
              </p>
            </div>

            {/* Connection Status */}
            <div className='group bg-gradient-to-br from-white to-green-50/50 p-6 rounded-2xl border border-green-200/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
              <div className='flex items-center justify-between mb-3'>
                <div className='p-2 bg-green-100 rounded-lg group-hover:scale-110 transition-transform duration-300'>
                  <Activity className='h-5 w-5 text-green-600' />
                </div>
                <div className='flex items-center space-x-1'>
                  <div className='w-1 h-1 bg-green-500 rounded-full animate-pulse'></div>
                  <div
                    className='w-1 h-1 bg-green-500 rounded-full animate-pulse'
                    style={{ animationDelay: "0.2s" }}
                  ></div>
                  <div
                    className='w-1 h-1 bg-green-500 rounded-full animate-pulse'
                    style={{ animationDelay: "0.4s" }}
                  ></div>
                </div>
              </div>
              <p className='text-xs text-gray-500 uppercase tracking-wider font-semibold mb-2'>
                Connection
              </p>
              <div className='flex items-center'>
                <div className='w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse'></div>
                <p className='text-xl font-bold text-green-600'>Connected</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default DeliveryDashboard;
