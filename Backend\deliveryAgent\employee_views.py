"""
Employee Management Views for Delivery Agents
Admin-controlled employee creation and management
"""

import json
import uuid
from datetime import datetime, date
from decimal import Decimal

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views import View
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.db import transaction, models
from django.utils import timezone
from django.contrib.auth import get_user_model

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from .models import DeliveryAgentProfile
from .serializers import DeliveryAgentProfileSerializer

User = get_user_model()


class AdminEmployeeCreateView(APIView):
    """Admin creates delivery agent employee accounts"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Create new delivery agent employee"""
        # Check if user is admin
        if request.user.role != 'admin':
            return Response({
                'status': 'error',
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        try:
            data = request.data
            
            # Validate required fields
            required_fields = [
                'full_name', 'father_name', 'national_id',
                'phone_number', 'hire_date', 'salary_type',
                'vehicle_type', 'province', 'district', 'area',
                'reference1_name', 'reference1_phone', 'reference1_relation',
                'reference2_name', 'reference2_phone', 'reference2_relation'
            ]
            
            for field in required_fields:
                if not data.get(field):
                    return Response({
                        'status': 'error',
                        'message': f'Required field missing: {field}'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if national ID already exists
            if User.objects.filter(
                delivery_agent_profile__national_id=data.get('national_id')
            ).exists():
                return Response({
                    'status': 'error',
                    'message': 'National ID already registered'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if phone already exists
            if User.objects.filter(phone=data.get('phone_number')).exists():
                return Response({
                    'status': 'error',
                    'message': 'Phone number already registered'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            with transaction.atomic():
                # Generate employee credentials
                employee_number = self.generate_employee_number()
                agent_id = f"DA{employee_number}"
                temp_password = self.generate_temp_password()

                # Double-check that the agent_id doesn't exist (safety check)
                if User.objects.filter(user_name=agent_id).exists():
                    return Response({
                        'status': 'error',
                        'message': f'Agent ID {agent_id} already exists. Please try again.'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Create user account
                user = User.objects.create_user(
                    user_name=agent_id,  # Use agent ID as username
                    name=data.get('full_name'),
                    phone=data.get('phone_number'),
                    email=data.get('email', ''),
                    password=temp_password,
                    role='delivery_agent',
                    is_active=True  # Active immediately for employees
                )
                
                # Create agent profile
                agent_profile = DeliveryAgentProfile.objects.create(
                    user=user,
                    agent_id=agent_id,
                    employee_number=employee_number,
                    
                    # Personal Information
                    full_name=data.get('full_name'),
                    father_name=data.get('father_name'),
                    national_id=data.get('national_id'),
                    date_of_birth=data.get('date_of_birth'),
                    gender=data.get('gender', 'male'),
                    marital_status=data.get('marital_status', 'single'),
                    
                    # Contact Information
                    phone_number=data.get('phone_number'),
                    secondary_phone=data.get('secondary_phone', ''),
                    email=data.get('email', ''),
                    
                    # Address Information
                    province=data.get('province'),
                    district=data.get('district'),
                    area=data.get('area'),
                    street_address=data.get('street_address', ''),
                    nearby_landmark=data.get('nearby_landmark', ''),
                    
                    # Vehicle Information
                    vehicle_type=data.get('vehicle_type'),
                    vehicle_model=data.get('vehicle_model', ''),
                    vehicle_year=data.get('vehicle_year'),
                    license_plate=data.get('license_plate', ''),
                    vehicle_color=data.get('vehicle_color', ''),
                    driving_license=data.get('driving_license', ''),
                    
                    # Employment Information
                    employment_status='active',
                    hire_date=data.get('hire_date'),
                    salary_type=data.get('salary_type'),
                    base_salary=Decimal(str(data.get('base_salary', '15000.00'))),
                    commission_per_delivery=Decimal(str(data.get('commission_per_delivery', '100.00'))),
                    hourly_rate=Decimal(str(data.get('hourly_rate', '0.00'))),
                    
                    # Work Schedule
                    work_schedule=data.get('work_schedule', 'full_time'),
                    shift_start_time=data.get('shift_start_time', '08:00'),
                    shift_end_time=data.get('shift_end_time', '17:00'),
                    working_days=data.get('working_days', 'monday_to_saturday'),
                    
                    # Banking Information
                    bank_name=data.get('bank_name', ''),
                    account_number=data.get('account_number', ''),
                    account_holder_name=data.get('account_holder_name', ''),
                    mobile_wallet=data.get('mobile_wallet', ''),
                    
                    # References
                    reference1_name=data.get('reference1_name'),
                    reference1_phone=data.get('reference1_phone'),
                    reference1_relation=data.get('reference1_relation'),
                    reference2_name=data.get('reference2_name'),
                    reference2_phone=data.get('reference2_phone'),
                    reference2_relation=data.get('reference2_relation'),

                    # Emergency Contact
                    emergency_contact=data.get('emergency_contact', ''),
                    emergency_relation=data.get('emergency_relation', ''),

                    # Set supervisor
                    supervisor=request.user,
                    
                    # Employee defaults
                    availability='offline',
                    is_verified=True,
                    training_completed=data.get('training_completed', False),
                    documents_complete=data.get('documents_complete', False),
                    background_check_completed=data.get('background_check_completed', False),
                    medical_clearance=data.get('medical_clearance', False),
                    
                    admin_notes=data.get('admin_notes', f'Employee created by {request.user.name} on {timezone.now().date()}'),
                    performance_notes=data.get('performance_notes', '')
                )
                
                return Response({
                    'status': 'success',
                    'message': 'Employee created successfully',
                    'data': {
                        'agent_id': agent_id,
                        'employee_number': employee_number,
                        'username': agent_id,
                        'temporary_password': temp_password,
                        'full_name': agent_profile.full_name,
                        'phone_number': agent_profile.phone_number,
                        'hire_date': agent_profile.hire_date,
                        'salary_type': agent_profile.salary_type,
                        'base_salary': str(agent_profile.base_salary),
                        'login_instructions': {
                            'website': 'http://localhost:5173/login',
                            'username': agent_id,
                            'password': temp_password,
                            'first_login_required': True,
                            'password_change_required': True
                        },
                        'account_details': {
                            'role': 'delivery_agent',
                            'status': 'active',
                            'email': user.email or 'Not provided',
                            'phone': user.phone,
                            'created_date': timezone.now().date().isoformat()
                        },
                        'next_steps': [
                            'Provide login credentials to employee',
                            'Employee must change password on first login',
                            'Schedule training session',
                            'Collect required documents',
                            'Complete background check',
                            'Set up work schedule'
                        ]
                    }
                }, status=status.HTTP_201_CREATED)
                
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Failed to create employee: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def generate_employee_number(self):
        """Generate unique sequential employee number"""
        from django.db import transaction

        with transaction.atomic():
            # Get the highest employee number that exists
            last_agent = DeliveryAgentProfile.objects.filter(
                employee_number__isnull=False
            ).order_by('-employee_number').first()

            if last_agent and last_agent.employee_number:
                try:
                    last_num = int(last_agent.employee_number)
                    next_num = last_num + 1
                except ValueError:
                    next_num = 1
            else:
                next_num = 1

            # Ensure uniqueness by checking if the number already exists
            while True:
                employee_number = str(next_num).zfill(3)
                agent_id = f"DA{employee_number}"

                # Check if this agent_id or employee_number already exists
                if not DeliveryAgentProfile.objects.filter(
                    models.Q(agent_id=agent_id) | models.Q(employee_number=employee_number)
                ).exists() and not User.objects.filter(user_name=agent_id).exists():
                    return employee_number

                next_num += 1

                # Safety check to prevent infinite loop
                if next_num > 9999:
                    raise ValueError("Unable to generate unique employee number")
    
    def generate_temp_password(self):
        """Generate temporary password for new employee"""
        import random
        import string

        # Generate a more user-friendly temporary password
        # Format: DA + 4 digits + 2 letters (e.g., DA1234AB)
        digits = ''.join(random.choices(string.digits, k=4))
        letters = ''.join(random.choices(string.ascii_uppercase, k=2))
        return f"DA{digits}{letters}"


class AdminEmployeeListView(APIView):
    """List all delivery agent employees"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get list of all employees"""
        if request.user.role != 'admin':
            return Response({
                'status': 'error',
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        try:
            # Get filter parameters
            employment_status = request.GET.get('employment_status', 'all')
            availability = request.GET.get('availability', 'all')
            
            # Base queryset
            queryset = DeliveryAgentProfile.objects.select_related('user', 'supervisor').all()
            
            # Apply filters
            if employment_status != 'all':
                queryset = queryset.filter(employment_status=employment_status)
            
            if availability != 'all':
                queryset = queryset.filter(availability=availability)
            
            # Serialize data
            employees = []
            for agent in queryset:
                employees.append({
                    'id': agent.id,
                    'agent_id': agent.agent_id,
                    'employee_number': agent.employee_number,
                    'full_name': agent.full_name,
                    'phone_number': agent.phone_number,
                    'employment_status': agent.employment_status,
                    'availability': agent.availability,
                    'hire_date': agent.hire_date,
                    'salary_type': agent.salary_type,
                    'base_salary': str(agent.base_salary),
                    'is_clocked_in': agent.is_clocked_in,
                    'total_deliveries': agent.total_deliveries,
                    'rating': str(agent.rating),
                    'supervisor': agent.supervisor.name if agent.supervisor else None,
                    'last_active': agent.last_active,
                    'created_at': agent.created_at
                })
            
            return Response({
                'status': 'success',
                'data': {
                    'employees': employees,
                    'total_count': len(employees),
                    'active_count': len([e for e in employees if e['employment_status'] == 'active']),
                    'online_count': len([e for e in employees if e['availability'] in ['available', 'busy']]),
                    'clocked_in_count': len([e for e in employees if e['is_clocked_in']])
                }
            })
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Failed to fetch employees: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdminEmployeeDetailView(APIView):
    """Get, update, or delete employee details"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, agent_id):
        """Get employee details"""
        if request.user.role != 'admin':
            return Response({
                'status': 'error',
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        try:
            agent = get_object_or_404(DeliveryAgentProfile, agent_id=agent_id)
            
            return Response({
                'status': 'success',
                'data': {
                    'id': agent.id,
                    'agent_id': agent.agent_id,
                    'employee_number': agent.employee_number,
                    'user_id': agent.user.id,
                    'username': agent.user.user_name,
                    
                    # Personal Information
                    'full_name': agent.full_name,
                    'father_name': agent.father_name,
                    'national_id': agent.national_id,
                    'date_of_birth': agent.date_of_birth,
                    'gender': agent.gender,
                    'marital_status': agent.marital_status,
                    
                    # Contact Information
                    'phone_number': agent.phone_number,
                    'secondary_phone': agent.secondary_phone,
                    'email': agent.email,
                    
                    # Address Information
                    'province': agent.province,
                    'district': agent.district,
                    'area': agent.area,
                    'street_address': agent.street_address,
                    'nearby_landmark': agent.nearby_landmark,
                    
                    # Vehicle Information
                    'vehicle_type': agent.vehicle_type,
                    'vehicle_model': agent.vehicle_model,
                    'vehicle_year': agent.vehicle_year,
                    'license_plate': agent.license_plate,
                    'vehicle_color': agent.vehicle_color,
                    'driving_license': agent.driving_license,
                    
                    # Employment Information
                    'employment_status': agent.employment_status,
                    'hire_date': agent.hire_date,
                    'termination_date': agent.termination_date,
                    'salary_type': agent.salary_type,
                    'base_salary': str(agent.base_salary),
                    'commission_per_delivery': str(agent.commission_per_delivery),
                    'hourly_rate': str(agent.hourly_rate),
                    
                    # Work Schedule
                    'work_schedule': agent.work_schedule,
                    'shift_start_time': agent.shift_start_time,
                    'shift_end_time': agent.shift_end_time,
                    'working_days': agent.working_days,
                    
                    # Current Status
                    'availability': agent.availability,
                    'is_clocked_in': agent.is_clocked_in,
                    'last_clock_in': agent.last_clock_in,
                    'last_clock_out': agent.last_clock_out,
                    'current_shift_hours': str(agent.current_shift_hours),
                    
                    # Performance
                    'total_deliveries': agent.total_deliveries,
                    'successful_deliveries': agent.successful_deliveries,
                    'completion_rate': agent.completion_rate,
                    'total_earnings': str(agent.total_earnings),
                    'rating': str(agent.rating),
                    
                    # Verification Status
                    'is_verified': agent.is_verified,
                    'training_completed': agent.training_completed,
                    'training_completion_date': agent.training_completion_date,
                    'documents_complete': agent.documents_complete,
                    'background_check_completed': agent.background_check_completed,
                    'medical_clearance': agent.medical_clearance,
                    
                    # Banking Information
                    'bank_name': agent.bank_name,
                    'account_number': agent.account_number,
                    'account_holder_name': agent.account_holder_name,
                    'mobile_wallet': agent.mobile_wallet,
                    
                    # Emergency Contact
                    'emergency_contact': agent.emergency_contact,
                    'emergency_relation': agent.emergency_relation,
                    
                    # Management
                    'supervisor': agent.supervisor.name if agent.supervisor else None,
                    'admin_notes': agent.admin_notes,
                    'performance_notes': agent.performance_notes,
                    
                    # Location
                    'current_latitude': str(agent.current_latitude) if agent.current_latitude else None,
                    'current_longitude': str(agent.current_longitude) if agent.current_longitude else None,
                    'current_address': agent.current_address,
                    'last_location_update': agent.last_location_update,
                    
                    # Timestamps
                    'created_at': agent.created_at,
                    'updated_at': agent.updated_at,
                    'last_active': agent.last_active
                }
            })
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Failed to fetch employee details: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, agent_id):
        """Update employee details"""
        if request.user.role != 'admin':
            return Response({
                'status': 'error',
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                # Get the agent profile
                agent = get_object_or_404(DeliveryAgentProfile, agent_id=agent_id)
                user = agent.user
                data = request.data

                # Update user information
                if data.get('full_name'):
                    user.name = data.get('full_name')
                    agent.full_name = data.get('full_name')

                if data.get('phone_number'):
                    user.phone = data.get('phone_number')
                    agent.phone_number = data.get('phone_number')

                if data.get('email'):
                    user.email = data.get('email')
                    agent.email = data.get('email')

                # Update agent profile fields
                updatable_fields = [
                    'father_name', 'national_id', 'date_of_birth', 'gender', 'marital_status',
                    'secondary_phone', 'province', 'district', 'area', 'street_address',
                    'nearby_landmark', 'vehicle_type', 'vehicle_model', 'vehicle_year',
                    'license_plate', 'vehicle_color', 'driving_license', 'employment_status',
                    'availability', 'hire_date', 'salary_type', 'base_salary',
                    'commission_per_delivery', 'hourly_rate', 'work_schedule',
                    'shift_start_time', 'shift_end_time', 'working_days', 'bank_name',
                    'account_number', 'account_holder_name', 'mobile_wallet',
                    'emergency_contact', 'emergency_relation', 'admin_notes',
                    'performance_notes'
                ]

                for field in updatable_fields:
                    if field in data:
                        setattr(agent, field, data[field])

                # Save changes
                user.save()
                agent.save()

                return Response({
                    'status': 'success',
                    'message': f'Employee {agent.full_name} updated successfully',
                    'data': {
                        'agent_id': agent.agent_id,
                        'full_name': agent.full_name,
                        'phone_number': agent.phone_number,
                        'email': agent.email,
                        'employment_status': agent.employment_status,
                        'updated_at': agent.updated_at
                    }
                })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Failed to update employee: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, agent_id):
        """Delete employee and associated user account"""
        if request.user.role != 'admin':
            return Response({
                'status': 'error',
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                # Get the agent profile
                agent = get_object_or_404(DeliveryAgentProfile, agent_id=agent_id)
                user = agent.user

                # Store info for response
                employee_info = {
                    'agent_id': agent.agent_id,
                    'full_name': agent.full_name,
                    'employee_number': agent.employee_number
                }

                # Update any orders assigned to this agent
                from orders.models import Order
                orders_updated = Order.objects.filter(delivery_agent=user).update(delivery_agent=None)

                # Delete the agent profile first
                agent.delete()

                # Delete the user account
                user.delete()

                return Response({
                    'status': 'success',
                    'message': f'Employee {employee_info["full_name"]} ({employee_info["agent_id"]}) has been deleted successfully',
                    'data': {
                        'deleted_employee': employee_info,
                        'orders_updated': orders_updated
                    }
                })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Failed to delete employee: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
