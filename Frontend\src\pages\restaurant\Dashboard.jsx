import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import {
  TrendingUp,
  DollarSign,
  ShoppingBag,
  Users,
  Calendar,
  ArrowUp,
  ArrowDown,
  Clock,
  Package,
  CheckCircle,
  XCircle,
  ChevronRight,
  BarChart2,
  PieChart,
  LineChart,
  Activity,
  Target,
  Zap,
  Star,
  Download,
  Filter,
  RefreshCw,
  TrendingDown,
  AlertTriangle,
  Award,
  Eye,
  Percent,
  Store,
  Utensils,
  Info,
  Shield,
  ShieldCheck,
  AlertCircle,
} from "lucide-react";
import { dashboardApi } from "../../utils/dashboardApi";
// Keep mock data as fallback
import { mockOrders } from "../../data/orders";
import { mockMenuItems } from "../../data/menuItems";
import { mockRestaurants } from "../../data/restaurants";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { DashboardSkeleton } from "../../components/skeleton/DashboardSkeleton";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import { Line, Bar, Pie, Doughnut } from "react-chartjs-2";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

function Dashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { restaurantId } = useParams();
  const {
    currentRestaurant,
    getRestaurant,
    getUserRestaurants,
    loading: restaurantLoading,
    error: restaurantError,
  } = useRestaurant();
  const [restaurant, setRestaurant] = useState(null);
  const [orders, setOrders] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("week");
  const [activeTab, setActiveTab] = useState("overview");
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalRevenue: 0,
    todayOrders: 0,
    todayRevenue: 0,
    pendingOrders: 0,
    completedOrders: 0,
    popularItems: [],
    revenueByDay: [],
    ordersByStatus: {},
    revenueByCategory: {},
    // Advanced analytics
    revenueGrowth: 0,
    avgOrderValue: 0,
    customerRetention: 0,
    peakHours: [],
    menuPerformance: [],
    profitMargins: {},
    forecastData: [],
    goals: {
      monthlyRevenue: 5000,
      dailyOrders: 20,
      avgOrderValue: 25,
    },
    achievements: [],
  });

  // Fetch restaurant data for current user
  useEffect(() => {
    const fetchRestaurantData = async () => {
      if (user && user.role === "restaurant") {
        try {
          setLoading(true);

          if (restaurantId) {
            // If restaurantId is provided in URL, fetch that specific restaurant
            const result = await getRestaurant(restaurantId);
            if (result.success) {
              setRestaurant(result.data);

              // Get real data from API
              try {
                const dashboardData = await dashboardApi.getDashboardData(restaurantId);

                if (dashboardData.success) {
                  const { orders, menuItems, analytics } = dashboardData.data;
                  setOrders(orders);
                  setMenuItems(menuItems);

                  // Use calculated analytics from API
                  setStats(prevStats => ({
                    ...prevStats,
                    ...analytics,
                    popularItems: menuItems.slice(0, 5), // Top 5 menu items
                    goals: prevStats.goals, // Keep existing goals
                    achievements: prevStats.achievements, // Keep existing achievements
                  }));
                } else {
                  // Fallback to mock data
                  console.warn("Failed to fetch real data, using mock data:", dashboardData.error);
                  const restaurantOrders = mockOrders.filter(
                    (order) => order.restaurantId === parseInt(restaurantId)
                  );
                  setOrders(restaurantOrders);

                  const restaurantMenuItems = mockMenuItems.filter(
                    (item) => item.restaurantId === parseInt(restaurantId)
                  );
                  setMenuItems(restaurantMenuItems);

                  // Calculate statistics from mock data
                  calculateStats(restaurantOrders, restaurantMenuItems);
                }
              } catch (error) {
                console.error("Error fetching dashboard data:", error);
                // Fallback to mock data
                const restaurantOrders = mockOrders.filter(
                  (order) => order.restaurantId === parseInt(restaurantId)
                );
                setOrders(restaurantOrders);

                const restaurantMenuItems = mockMenuItems.filter(
                  (item) => item.restaurantId === parseInt(restaurantId)
                );
                setMenuItems(restaurantMenuItems);

                calculateStats(restaurantOrders, restaurantMenuItems);
              }
            } else {
              console.error("Failed to fetch restaurant:", result.error);
              navigate("/restaurant/my-restaurants");
              return;
            }
          } else {
            // Fetch all restaurants owned by the current user
            const result = await getUserRestaurants();

            if (result.success && result.data.length > 0) {
              // If user has multiple restaurants, redirect to My Restaurants page
              if (result.data.length > 1) {
                navigate("/restaurant/my-restaurants");
                return;
              }

              // If user has only one restaurant, show its dashboard
              const userRestaurant = result.data[0];
              setRestaurant(userRestaurant);

              // Get real data from API
              try {
                const dashboardData = await dashboardApi.getDashboardData(userRestaurant.id);

                if (dashboardData.success) {
                  const { orders, menuItems, analytics } = dashboardData.data;
                  setOrders(orders);
                  setMenuItems(menuItems);

                  // Use calculated analytics from API
                  setStats(prevStats => ({
                    ...prevStats,
                    ...analytics,
                    popularItems: menuItems.slice(0, 5), // Top 5 menu items
                    goals: prevStats.goals, // Keep existing goals
                    achievements: prevStats.achievements, // Keep existing achievements
                  }));
                } else {
                  // Fallback to mock data
                  console.warn("Failed to fetch real data, using mock data:", dashboardData.error);
                  const restaurantOrders = mockOrders.filter(
                    (order) => order.restaurantId === userRestaurant.id
                  );
                  setOrders(restaurantOrders);

                  const restaurantMenuItems = mockMenuItems.filter(
                    (item) => item.restaurantId === userRestaurant.id
                  );
                  setMenuItems(restaurantMenuItems);

                  // Calculate statistics from mock data
                  calculateStats(restaurantOrders, restaurantMenuItems);
                }
              } catch (error) {
                console.error("Error fetching dashboard data:", error);
                // Fallback to mock data
                const restaurantOrders = mockOrders.filter(
                  (order) => order.restaurantId === userRestaurant.id
                );
                setOrders(restaurantOrders);

                const restaurantMenuItems = mockMenuItems.filter(
                  (item) => item.restaurantId === userRestaurant.id
                );
                setMenuItems(restaurantMenuItems);

                calculateStats(restaurantOrders, restaurantMenuItems);
              }
            } else {
              // Fallback to mock data if no restaurants found
              let userRestaurant = mockRestaurants.find(
                (r) => r.ownerId === user.id
              );

              // If no restaurant found for current user, use first restaurant as demo
              if (!userRestaurant && mockRestaurants.length > 0) {
                userRestaurant = mockRestaurants[0];
                // Update the restaurant to show it belongs to current user
                userRestaurant = { ...userRestaurant, ownerId: user.id };
              }

              if (userRestaurant) {
                setRestaurant(userRestaurant);

                // Try to get real data, fallback to mock data
                try {
                  const dashboardData = await dashboardApi.getDashboardData(userRestaurant.id);

                  if (dashboardData.success) {
                    const { orders, menuItems, analytics } = dashboardData.data;
                    setOrders(orders);
                    setMenuItems(menuItems);

                    // Use calculated analytics from API
                    setStats(prevStats => ({
                      ...prevStats,
                      ...analytics,
                      popularItems: menuItems.slice(0, 5), // Top 5 menu items
                      goals: prevStats.goals, // Keep existing goals
                      achievements: prevStats.achievements, // Keep existing achievements
                    }));
                  } else {
                    // Use mock data as final fallback
                    const restaurantOrders = mockOrders.filter(
                      (order) => order.restaurantId === userRestaurant.id
                    );
                    setOrders(restaurantOrders);

                    const restaurantMenuItems = mockMenuItems.filter(
                      (item) => item.restaurantId === userRestaurant.id
                    );
                    setMenuItems(restaurantMenuItems);

                    calculateStats(restaurantOrders, restaurantMenuItems);
                  }
                } catch (error) {
                  console.error("Error fetching dashboard data:", error);
                  // Use mock data as final fallback
                  const restaurantOrders = mockOrders.filter(
                    (order) => order.restaurantId === userRestaurant.id
                  );
                  setOrders(restaurantOrders);

                  const restaurantMenuItems = mockMenuItems.filter(
                    (item) => item.restaurantId === userRestaurant.id
                  );
                  setMenuItems(restaurantMenuItems);

                  calculateStats(restaurantOrders, restaurantMenuItems);
                }
              }
            }
          }
        } catch (error) {
          console.error("Error fetching restaurant data:", error);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    fetchRestaurantData();
  }, [user, navigate, restaurantId, getRestaurant, getUserRestaurants]);

  // Recalculate stats when time range changes
  useEffect(() => {
    if (orders.length > 0 && menuItems.length > 0) {
      calculateStats(orders, menuItems);
    }
  }, [timeRange, orders, menuItems]);

  const calculateStats = (orders, menuItems) => {
    // Get date range based on selected time range
    const now = new Date();
    let startDate;

    switch (timeRange) {
      case "today":
        startDate = new Date(now.setHours(0, 0, 0, 0));
        break;
      case "week":
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 7);
        break;
      case "month":
        startDate = new Date(now);
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case "year":
        startDate = new Date(now);
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 7);
    }

    // Filter orders by date range
    const filteredOrders = orders.filter((order) => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= startDate;
    });

    // Calculate total orders and revenue
    const totalOrders = filteredOrders.length;
    const totalRevenue = filteredOrders.reduce(
      (sum, order) => sum + order.totalAmount,
      0
    );

    // Calculate today's orders and revenue
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayOrders = filteredOrders.filter((order) => {
      const orderDate = new Date(order.createdAt);
      orderDate.setHours(0, 0, 0, 0);
      return orderDate.getTime() === today.getTime();
    }).length;

    const todayRevenue = filteredOrders
      .filter((order) => {
        const orderDate = new Date(order.createdAt);
        orderDate.setHours(0, 0, 0, 0);
        return orderDate.getTime() === today.getTime();
      })
      .reduce((sum, order) => sum + order.totalAmount, 0);

    // Calculate pending and completed orders
    const pendingOrders = filteredOrders.filter((order) =>
      [
        "pending",
        "confirmed",
        "preparing",
        "readyForPickup",
        "outForDelivery",
      ].includes(order.status)
    ).length;

    const completedOrders = filteredOrders.filter(
      (order) => order.status === "delivered"
    ).length;

    // Calculate popular items
    const itemOrderCounts = {};
    const itemRevenue = {};

    filteredOrders.forEach((order) => {
      order.orderItems.forEach((item) => {
        if (!itemOrderCounts[item.menuItemId]) {
          itemOrderCounts[item.menuItemId] = 0;
          itemRevenue[item.menuItemId] = 0;
        }
        itemOrderCounts[item.menuItemId] += item.quantity;
        itemRevenue[item.menuItemId] += item.price * item.quantity;
      });
    });

    const popularItems = Object.keys(itemOrderCounts)
      .map((itemId) => {
        const menuItem = menuItems.find((item) => item.id === itemId);
        return {
          id: itemId,
          name: menuItem ? menuItem.name : "Unknown Item",
          count: itemOrderCounts[itemId],
          revenue: itemRevenue[itemId],
          image: menuItem ? menuItem.image : "",
          category: menuItem ? menuItem.category : "Unknown",
        };
      })
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Calculate revenue by day for the past week
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date;
    }).reverse();

    const revenueByDay = last7Days.map((date) => {
      const dayStr = date.toLocaleDateString("en-US", { weekday: "short" });
      const dayRevenue = filteredOrders
        .filter((order) => {
          const orderDate = new Date(order.createdAt);
          return (
            orderDate.getDate() === date.getDate() &&
            orderDate.getMonth() === date.getMonth() &&
            orderDate.getFullYear() === date.getFullYear()
          );
        })
        .reduce((sum, order) => sum + order.totalAmount, 0);

      return { day: dayStr, revenue: dayRevenue };
    });

    // Calculate orders by status
    const ordersByStatus = {};
    filteredOrders.forEach((order) => {
      if (!ordersByStatus[order.status]) {
        ordersByStatus[order.status] = 0;
      }
      ordersByStatus[order.status]++;
    });

    // Calculate revenue by category
    const revenueByCategory = {};
    filteredOrders.forEach((order) => {
      order.orderItems.forEach((item) => {
        const menuItem = menuItems.find((mi) => mi.id === item.menuItemId);
        if (menuItem) {
          const category = menuItem.category;
          if (!revenueByCategory[category]) {
            revenueByCategory[category] = 0;
          }
          revenueByCategory[category] += item.price * item.quantity;
        }
      });
    });

    // Advanced Analytics Calculations

    // Calculate revenue growth (comparing to previous period)
    const previousPeriodStart = new Date(startDate);
    const periodLength = now.getTime() - startDate.getTime();
    previousPeriodStart.setTime(previousPeriodStart.getTime() - periodLength);

    const previousPeriodOrders = orders.filter((order) => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= previousPeriodStart && orderDate < startDate;
    });

    const previousRevenue = previousPeriodOrders.reduce(
      (sum, order) => sum + order.totalAmount,
      0
    );

    const revenueGrowth =
      previousRevenue > 0
        ? ((totalRevenue - previousRevenue) / previousRevenue) * 100
        : 0;

    // Calculate average order value
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate customer retention (simplified)
    const uniqueCustomers = new Set(
      filteredOrders.map((order) => order.customerId)
    );
    const repeatCustomers = new Set();

    uniqueCustomers.forEach((customerId) => {
      const customerOrders = filteredOrders.filter(
        (order) => order.customerId === customerId
      );
      if (customerOrders.length > 1) {
        repeatCustomers.add(customerId);
      }
    });

    const customerRetention =
      uniqueCustomers.size > 0
        ? (repeatCustomers.size / uniqueCustomers.size) * 100
        : 0;

    // Calculate peak hours
    const hourlyOrders = {};
    filteredOrders.forEach((order) => {
      const hour = new Date(order.createdAt).getHours();
      hourlyOrders[hour] = (hourlyOrders[hour] || 0) + 1;
    });

    const peakHours = Object.entries(hourlyOrders)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([hour, count]) => ({
        hour: parseInt(hour),
        count,
        timeRange: `${hour}:00 - ${parseInt(hour) + 1}:00`,
      }));

    // Calculate menu performance with profitability
    const menuPerformance = popularItems.map((item) => {
      const menuItem = menuItems.find((mi) => mi.id === item.id);
      const costPrice = menuItem?.price * 0.4 || 0; // Assume 40% cost
      const profit = item.revenue - costPrice * item.count;
      const profitMargin = item.revenue > 0 ? (profit / item.revenue) * 100 : 0;

      return {
        ...item,
        profit,
        profitMargin,
        costPrice,
        salesVelocity: (item.count / Math.max(1, totalOrders)) * 100,
      };
    });

    // Generate forecast data (simple trend-based)
    const forecastData = revenueByDay.map((day, index) => {
      const trend =
        index > 0 ? day.revenue - revenueByDay[index - 1].revenue : 0;
      return {
        day: day.day,
        actual: day.revenue,
        forecast: Math.max(0, day.revenue + trend * 1.1),
      };
    });

    // Calculate profit margins by category
    const profitMargins = {};
    Object.keys(revenueByCategory).forEach((category) => {
      const categoryRevenue = revenueByCategory[category];
      const estimatedCost = categoryRevenue * 0.4; // 40% cost assumption
      profitMargins[category] = {
        revenue: categoryRevenue,
        cost: estimatedCost,
        profit: categoryRevenue - estimatedCost,
        margin: ((categoryRevenue - estimatedCost) / categoryRevenue) * 100,
      };
    });

    // Generate achievements based on performance
    const achievements = [];
    if (revenueGrowth > 20)
      achievements.push({
        type: "growth",
        message: "High Revenue Growth!",
        icon: "trending-up",
      });
    if (customerRetention > 50)
      achievements.push({
        type: "retention",
        message: "Great Customer Retention!",
        icon: "users",
      });
    if (avgOrderValue > 30)
      achievements.push({
        type: "value",
        message: "High Average Order Value!",
        icon: "dollar-sign",
      });
    if (completedOrders > 50)
      achievements.push({
        type: "volume",
        message: "High Order Volume!",
        icon: "package",
      });

    setStats({
      totalOrders,
      totalRevenue,
      todayOrders,
      todayRevenue,
      pendingOrders,
      completedOrders,
      popularItems,
      revenueByDay,
      ordersByStatus,
      revenueByCategory,
      // Advanced analytics
      revenueGrowth,
      avgOrderValue,
      customerRetention,
      peakHours,
      menuPerformance,
      profitMargins,
      forecastData,
      goals: {
        monthlyRevenue: 5000,
        dailyOrders: 20,
        avgOrderValue: 25,
      },
      achievements,
    });
  };

  // Chart data for revenue by day
  const revenueChartData = {
    labels: stats.revenueByDay.map((item) => item.day),
    datasets: [
      {
        label: "Revenue",
        data: stats.revenueByDay.map((item) => item.revenue),
        borderColor: "rgb(99, 102, 241)",
        backgroundColor: "rgba(99, 102, 241, 0.1)",
        tension: 0.4,
        fill: true,
      },
    ],
  };

  // Chart data for orders by status
  const orderStatusChartData = {
    labels: Object.keys(stats.ordersByStatus).map(
      (status) => status.charAt(0).toUpperCase() + status.slice(1)
    ),
    datasets: [
      {
        label: "Orders",
        data: Object.values(stats.ordersByStatus),
        backgroundColor: [
          "rgba(255, 99, 132, 0.7)",
          "rgba(54, 162, 235, 0.7)",
          "rgba(255, 206, 86, 0.7)",
          "rgba(75, 192, 192, 0.7)",
          "rgba(153, 102, 255, 0.7)",
          "rgba(255, 159, 64, 0.7)",
          "rgba(199, 199, 199, 0.7)",
        ],
        borderWidth: 1,
      },
    ],
  };

  // Chart data for revenue by category
  const categoryChartData = {
    labels: Object.keys(stats.revenueByCategory),
    datasets: [
      {
        label: "Revenue by Category",
        data: Object.values(stats.revenueByCategory),
        backgroundColor: [
          "rgba(255, 99, 132, 0.7)",
          "rgba(54, 162, 235, 0.7)",
          "rgba(255, 206, 86, 0.7)",
          "rgba(75, 192, 192, 0.7)",
          "rgba(153, 102, 255, 0.7)",
          "rgba(255, 159, 64, 0.7)",
        ],
        borderWidth: 1,
      },
    ],
  };

  // Chart data for popular items
  const popularItemsChartData = {
    labels: stats.popularItems.map((item) => item.name),
    datasets: [
      {
        label: "Orders",
        data: stats.popularItems.map((item) => item.count),
        backgroundColor: [
          "rgba(255, 99, 132, 0.7)",
          "rgba(54, 162, 235, 0.7)",
          "rgba(255, 206, 86, 0.7)",
          "rgba(75, 192, 192, 0.7)",
          "rgba(153, 102, 255, 0.7)",
        ],
        borderWidth: 1,
      },
    ],
  };

  // Chart data for revenue forecast
  const forecastChartData = {
    labels: stats.forecastData.map((item) => item.day),
    datasets: [
      {
        label: "Actual Revenue",
        data: stats.forecastData.map((item) => item.actual),
        borderColor: "rgb(99, 102, 241)",
        backgroundColor: "rgba(99, 102, 241, 0.1)",
        tension: 0.4,
        fill: false,
      },
      {
        label: "Forecasted Revenue",
        data: stats.forecastData.map((item) => item.forecast),
        borderColor: "rgb(34, 197, 94)",
        backgroundColor: "rgba(34, 197, 94, 0.1)",
        borderDash: [5, 5],
        tension: 0.4,
        fill: false,
      },
    ],
  };

  // Chart data for menu performance
  const menuPerformanceChartData = {
    labels: stats.menuPerformance.map((item) => item.name),
    datasets: [
      {
        label: "Profit Margin (%)",
        data: stats.menuPerformance.map((item) => item.profitMargin),
        backgroundColor: stats.menuPerformance.map((item) =>
          item.profitMargin > 60
            ? "rgba(34, 197, 94, 0.7)"
            : item.profitMargin > 40
            ? "rgba(255, 206, 86, 0.7)"
            : "rgba(239, 68, 68, 0.7)"
        ),
        borderWidth: 1,
      },
    ],
  };

  // Chart data for peak hours
  const peakHoursChartData = {
    labels: stats.peakHours.map((item) => item.timeRange),
    datasets: [
      {
        label: "Orders",
        data: stats.peakHours.map((item) => item.count),
        backgroundColor: [
          "rgba(255, 99, 132, 0.7)",
          "rgba(54, 162, 235, 0.7)",
          "rgba(255, 206, 86, 0.7)",
        ],
        borderWidth: 1,
      },
    ],
  };

  // Utility functions
  const handleRefresh = async () => {
    if (!restaurant?.id) return;

    setRefreshing(true);
    try {
      const dashboardData = await dashboardApi.getDashboardData(restaurant.id);

      if (dashboardData.success) {
        const { orders, menuItems, analytics } = dashboardData.data;
        setOrders(orders);
        setMenuItems(menuItems);

        // Use calculated analytics from API
        setStats(prevStats => ({
          ...prevStats,
          ...analytics,
          popularItems: menuItems.slice(0, 5), // Top 5 menu items
          goals: prevStats.goals, // Keep existing goals
          achievements: prevStats.achievements, // Keep existing achievements
        }));
      } else {
        // Fallback to recalculating existing data
        calculateStats(orders, menuItems);
      }
    } catch (error) {
      console.error("Error refreshing dashboard data:", error);
      // Fallback to recalculating existing data
      calculateStats(orders, menuItems);
    } finally {
      setRefreshing(false);
    }
  };

  const getGoalProgress = (current, goal) => {
    return Math.min((current / goal) * 100, 100);
  };

  if (loading) {
    return (
      <div className='p-6 animate-fade-in'>
        <DashboardSkeleton userRole='restaurant' />
      </div>
    );
  }

  // Show welcome message for new restaurants without data
  if (!restaurant) {
    return (
      <div className='p-6 animate-fade-in'>
        <div className='mx-auto max-w-4xl'>
          {/* Welcome Header */}
          <div className='mb-8 text-center'>
            <div className='flex justify-center items-center bg-primary-100 mx-auto mb-4 rounded-full w-20 h-20'>
              <Store className='text-primary-600' size={40} />
            </div>
            <h1 className='mb-2 font-bold text-gray-900 text-3xl'>
              Welcome to Afghan Sofra!
            </h1>
            <p className='mx-auto max-w-2xl text-text-secondary'>
              Your restaurant dashboard will show analytics, orders, and
              performance metrics once you complete your setup.
            </p>
          </div>

          {/* Quick Setup Cards */}
          <div className='gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mb-8'>
            <Card className='hover:shadow-lg p-6 text-center transition-shadow'>
              <div className='flex justify-center items-center bg-blue-100 mx-auto mb-4 rounded-full w-12 h-12'>
                <Utensils className='text-blue-600' size={24} />
              </div>
              <h3 className='mb-2 font-semibold'>Complete Your Profile</h3>
              <p className='mb-4 text-text-secondary text-sm'>
                Add restaurant details, photos, and operating hours
              </p>
              <Button variant='outline' size='small' to='/restaurant/profile'>
                Update Profile
              </Button>
            </Card>

            <Card className='hover:shadow-lg p-6 text-center transition-shadow'>
              <div className='flex justify-center items-center bg-green-100 mx-auto mb-4 rounded-full w-12 h-12'>
                <Package className='text-green-600' size={24} />
              </div>
              <h3 className='mb-2 font-semibold'>Add Your Menu</h3>
              <p className='mb-4 text-text-secondary text-sm'>
                Create categories and add delicious menu items
              </p>
              <Button variant='outline' size='small' to='/restaurant/menu'>
                Manage Menu
              </Button>
            </Card>

            <Card className='hover:shadow-lg p-6 text-center transition-shadow'>
              <div className='flex justify-center items-center bg-purple-100 mx-auto mb-4 rounded-full w-12 h-12'>
                <ShoppingBag className='text-purple-600' size={24} />
              </div>
              <h3 className='mb-2 font-semibold'>Start Receiving Orders</h3>
              <p className='mb-4 text-text-secondary text-sm'>
                Once setup is complete, you'll start receiving orders
              </p>
              <Button variant='outline' size='small' to='/restaurant/orders'>
                View Orders
              </Button>
            </Card>
          </div>

          {/* Help Section */}
          <Card className='bg-gradient-to-r from-primary-50 to-primary-100 p-6 border-primary-200'>
            <div className='flex items-start space-x-4'>
              <div className='flex flex-shrink-0 justify-center items-center bg-primary-100 rounded-full w-12 h-12'>
                <Info className='text-primary-600' size={24} />
              </div>
              <div className='flex-1'>
                <h3 className='mb-2 font-semibold text-primary-800'>
                  Need Help Getting Started?
                </h3>
                <p className='mb-4 text-primary-700'>
                  Our team is here to help you set up your restaurant and start
                  receiving orders quickly.
                </p>
                <div className='flex sm:flex-row flex-col gap-3'>
                  <Button
                    variant='primary'
                    size='small'
                    to='/restaurant/onboarding'
                  >
                    Complete Onboarding
                  </Button>
                  <Button
                    variant='outline'
                    size='small'
                    onClick={() => {
                      // Demo: Load sample restaurant data
                      const sampleRestaurant = mockRestaurants[0];
                      setRestaurant({ ...sampleRestaurant, ownerId: user.id });
                      const sampleOrders = mockOrders.filter(
                        (order) => order.restaurantId === sampleRestaurant.id
                      );
                      setOrders(sampleOrders);
                      const sampleMenuItems = mockMenuItems.filter(
                        (item) => item.restaurantId === sampleRestaurant.id
                      );
                      setMenuItems(sampleMenuItems);
                      calculateStats(sampleOrders, sampleMenuItems);
                    }}
                  >
                    Load Demo Data
                  </Button>
                  <Button variant='outline' size='small'>
                    Contact Support
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      {/* Restaurant Header */}
      <div className='bg-gradient-to-r from-primary-50 to-primary-100 mb-6 p-6 rounded-lg'>
        <div className='flex items-center space-x-4'>
          <img
            src={restaurant.logo}
            alt={restaurant.name}
            className='shadow-lg border-4 border-white rounded-full w-16 h-16 object-cover'
          />
          <div className='flex-1'>
            <h1 className='font-bold text-gray-900 text-2xl'>
              {restaurant.name}
            </h1>
            <p className='text-text-secondary'>{restaurant.address}</p>
            <div className='flex items-center space-x-4 mt-2'>
              <div className='flex items-center space-x-1'>
                <Star className='fill-current text-yellow-400' size={16} />
                <span className='font-medium text-sm'>{restaurant.rating}</span>
              </div>
              <div className='flex items-center space-x-1'>
                <Clock className='text-gray-400' size={16} />
                <span className='text-sm'>{restaurant.deliveryTime}</span>
              </div>
              <Badge variant={restaurant.isOpen ? "success" : "danger"}>
                {restaurant.isOpen ? "Open" : "Closed"}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Verification Status Alert */}
      {restaurant && !restaurant.is_verified && (
        <div className='bg-yellow-50 mb-6 p-4 border border-yellow-200 rounded-lg'>
          <div className='flex items-start space-x-3'>
            <AlertCircle className='mt-0.5 text-yellow-600' size={20} />
            <div className='flex-1'>
              <h3 className='mb-1 font-medium text-yellow-800 text-sm'>
                Restaurant Verification Pending
              </h3>
              <p className='mb-3 text-yellow-700 text-sm'>
                Your restaurant is currently under review by our admin team. You
                cannot add menu categories or items until your restaurant is
                verified.
              </p>
              <div className='flex items-center space-x-4 text-yellow-600 text-xs'>
                <div className='flex items-center space-x-1'>
                  <Shield size={14} />
                  <span>Verification Required</span>
                </div>
                <div className='flex items-center space-x-1'>
                  <Clock size={14} />
                  <span>Usually takes 1-2 business days</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Verification Success Alert */}
      {restaurant && restaurant.is_verified && (
        <div className='bg-green-50 mb-6 p-4 border border-green-200 rounded-lg'>
          <div className='flex items-center space-x-3'>
            <ShieldCheck className='text-green-600' size={20} />
            <div className='flex-1'>
              <h3 className='font-medium text-green-800 text-sm'>
                Restaurant Verified ✅
              </h3>
              <p className='text-green-700 text-sm'>
                Your restaurant has been verified! You can now manage your menu
                categories and items.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Header */}
      <div className='flex lg:flex-row flex-col lg:justify-between lg:items-center mb-6'>
        <div className='flex items-center space-x-4 mb-4 lg:mb-0'>
          <h2 className='font-bold text-gray-900 text-2xl'>
            Analytics Dashboard
          </h2>
          <button
            onClick={handleRefresh}
            className={`p-2 rounded-full hover:bg-gray-100 transition-colors ${
              refreshing ? "animate-spin" : ""
            }`}
            disabled={refreshing}
          >
            <RefreshCw size={20} className='text-gray-600' />
          </button>
        </div>

        <div className='flex flex-wrap items-center space-x-2'>
          <div className='flex space-x-1 bg-gray-100 p-1 rounded-lg'>
            <Button
              variant={timeRange === "today" ? "primary" : "ghost"}
              size='small'
              onClick={() => setTimeRange("today")}
            >
              Today
            </Button>
            <Button
              variant={timeRange === "week" ? "primary" : "ghost"}
              size='small'
              onClick={() => setTimeRange("week")}
            >
              Week
            </Button>
            <Button
              variant={timeRange === "month" ? "primary" : "ghost"}
              size='small'
              onClick={() => setTimeRange("month")}
            >
              Month
            </Button>
            <Button
              variant={timeRange === "year" ? "primary" : "ghost"}
              size='small'
              onClick={() => setTimeRange("year")}
            >
              Year
            </Button>
          </div>

          <Button variant='outline' size='small' className='ml-2'>
            <Download size={16} className='mr-1' />
            Export
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className='flex space-x-1 mb-6 border-gray-200 border-b'>
        {[
          { id: "overview", label: "Overview", icon: BarChart2 },
          { id: "revenue", label: "Revenue Analytics", icon: TrendingUp },
          { id: "menu", label: "Menu Performance", icon: Package },
          { id: "customers", label: "Customer Insights", icon: Users },
          { id: "operations", label: "Operations", icon: Activity },
        ].map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-t-lg transition-colors ${
                activeTab === tab.id
                  ? "bg-primary-50 text-primary-600 border-b-2 border-primary-600"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              }`}
            >
              <Icon size={16} />
              <span className='font-medium'>{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Achievements Banner */}
      {stats.achievements.length > 0 && (
        <div className='mb-6'>
          <div className='bg-gradient-to-r from-green-50 to-blue-50 p-4 border border-green-200 rounded-lg'>
            <div className='flex items-center space-x-2 mb-2'>
              <Award className='text-green-600' size={20} />
              <h3 className='font-semibold text-green-800'>
                Recent Achievements
              </h3>
            </div>
            <div className='flex flex-wrap gap-2'>
              {stats.achievements.map((achievement, index) => (
                <Badge
                  key={index}
                  className='bg-green-100 px-3 py-1 text-green-800'
                >
                  {achievement.message}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Stats Cards */}
      <div className='gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-6'>
        {/* Total Revenue with Growth */}
        <Card className='bg-gradient-to-br from-green-50 to-green-100 p-6 border-green-200'>
          <div className='flex justify-between items-center mb-4'>
            <div className='bg-green-100 p-3 rounded-full'>
              <DollarSign size={24} className='text-green-600' />
            </div>
            <div className='text-right'>
              <div
                className={`flex items-center text-sm font-medium ${
                  stats.revenueGrowth >= 0 ? "text-green-600" : "text-red-600"
                }`}
              >
                {stats.revenueGrowth >= 0 ? (
                  <ArrowUp size={14} className='mr-1' />
                ) : (
                  <ArrowDown size={14} className='mr-1' />
                )}
                {Math.abs(stats.revenueGrowth).toFixed(1)}%
              </div>
              <p className='text-gray-500 text-xs'>vs last period</p>
            </div>
          </div>
          <h3 className='mb-1 font-medium text-gray-600 text-sm'>
            Total Revenue
          </h3>
          <p className='font-bold text-gray-900 text-3xl'>
            ${stats.totalRevenue.toFixed(2)}
          </p>
          <div className='mt-2'>
            <div className='flex justify-between mb-1 text-gray-500 text-xs'>
              <span>Goal: ${stats.goals.monthlyRevenue}</span>
              <span>
                {getGoalProgress(
                  stats.totalRevenue,
                  stats.goals.monthlyRevenue
                ).toFixed(0)}
                %
              </span>
            </div>
            <div className='bg-gray-200 rounded-full w-full h-2'>
              <div
                className='bg-green-500 rounded-full h-2 transition-all duration-300'
                style={{
                  width: `${getGoalProgress(
                    stats.totalRevenue,
                    stats.goals.monthlyRevenue
                  )}%`,
                }}
              ></div>
            </div>
          </div>
        </Card>

        {/* Average Order Value */}
        <Card className='bg-gradient-to-br from-blue-50 to-blue-100 p-6 border-blue-200'>
          <div className='flex justify-between items-center mb-4'>
            <div className='bg-blue-100 p-3 rounded-full'>
              <ShoppingBag size={24} className='text-blue-600' />
            </div>
            <div className='text-right'>
              <div
                className={`flex items-center text-sm font-medium ${
                  stats.avgOrderValue >= stats.goals.avgOrderValue
                    ? "text-green-600"
                    : "text-orange-600"
                }`}
              >
                <Target size={14} className='mr-1' />
                Goal
              </div>
              <p className='text-gray-500 text-xs'>
                ${stats.goals.avgOrderValue}
              </p>
            </div>
          </div>
          <h3 className='mb-1 font-medium text-gray-600 text-sm'>
            Avg Order Value
          </h3>
          <p className='font-bold text-gray-900 text-3xl'>
            ${stats.avgOrderValue.toFixed(2)}
          </p>
          <div className='mt-2'>
            <div className='flex justify-between mb-1 text-gray-500 text-xs'>
              <span>Total Orders: {stats.totalOrders}</span>
              <span>{stats.totalOrders > 0 ? "Active" : "No orders"}</span>
            </div>
          </div>
        </Card>

        {/* Customer Retention */}
        <Card className='bg-gradient-to-br from-purple-50 to-purple-100 p-6 border-purple-200'>
          <div className='flex justify-between items-center mb-4'>
            <div className='bg-purple-100 p-3 rounded-full'>
              <Users size={24} className='text-purple-600' />
            </div>
            <div className='text-right'>
              <div
                className={`flex items-center text-sm font-medium ${
                  stats.customerRetention > 50
                    ? "text-green-600"
                    : stats.customerRetention > 25
                    ? "text-orange-600"
                    : "text-red-600"
                }`}
              >
                <Percent size={14} className='mr-1' />
                {stats.customerRetention > 50
                  ? "Great"
                  : stats.customerRetention > 25
                  ? "Good"
                  : "Low"}
              </div>
              <p className='text-gray-500 text-xs'>retention rate</p>
            </div>
          </div>
          <h3 className='mb-1 font-medium text-gray-600 text-sm'>
            Customer Retention
          </h3>
          <p className='font-bold text-gray-900 text-3xl'>
            {stats.customerRetention.toFixed(1)}%
          </p>
          <div className='mt-2'>
            <div className='bg-gray-200 rounded-full w-full h-2'>
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  stats.customerRetention > 50
                    ? "bg-green-500"
                    : stats.customerRetention > 25
                    ? "bg-orange-500"
                    : "bg-red-500"
                }`}
                style={{
                  width: `${Math.min(stats.customerRetention, 100)}%`,
                }}
              ></div>
            </div>
          </div>
        </Card>

        {/* Order Completion Rate */}
        <Card className='bg-gradient-to-br from-orange-50 to-orange-100 p-6 border-orange-200'>
          <div className='flex justify-between items-center mb-4'>
            <div className='bg-orange-100 p-3 rounded-full'>
              <CheckCircle size={24} className='text-orange-600' />
            </div>
            <div className='text-right'>
              <div className='flex items-center font-medium text-gray-600 text-sm'>
                <Clock size={14} className='mr-1' />
                {stats.pendingOrders} pending
              </div>
              <p className='text-gray-500 text-xs'>active orders</p>
            </div>
          </div>
          <h3 className='mb-1 font-medium text-gray-600 text-sm'>
            Completion Rate
          </h3>
          <p className='font-bold text-gray-900 text-3xl'>
            {stats.totalOrders > 0
              ? ((stats.completedOrders / stats.totalOrders) * 100).toFixed(1)
              : 0}
            %
          </p>
          <div className='mt-2'>
            <div className='flex justify-between mb-1 text-gray-500 text-xs'>
              <span>Completed: {stats.completedOrders}</span>
              <span>Total: {stats.totalOrders}</span>
            </div>
          </div>
        </Card>
      </div>

      {/* Tab Content */}
      {activeTab === "overview" && (
        <>
          {/* Overview Charts */}
          <div className='gap-6 grid grid-cols-1 lg:grid-cols-2 mb-6'>
            {/* Revenue Chart */}
            <Card className='p-6'>
              <div className='flex justify-between items-center mb-6'>
                <h2 className='font-semibold text-lg'>Revenue Trend</h2>
                <div className='flex items-center text-gray-500 text-sm'>
                  <LineChart size={16} className='mr-1' />
                  Last 7 Days
                </div>
              </div>
              <div className='h-80'>
                <Line
                  data={revenueChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          callback: (value) => `$${value}`,
                        },
                      },
                    },
                    plugins: {
                      legend: {
                        display: false,
                      },
                      tooltip: {
                        callbacks: {
                          label: (context) =>
                            `Revenue: $${context.raw.toFixed(2)}`,
                        },
                      },
                    },
                  }}
                />
              </div>
            </Card>

            {/* Order Status Chart */}
            <Card className='p-6'>
              <div className='flex justify-between items-center mb-6'>
                <h2 className='font-semibold text-lg'>Orders by Status</h2>
                <div className='flex items-center text-gray-500 text-sm'>
                  <PieChart size={16} className='mr-1' />
                  Status Distribution
                </div>
              </div>
              <div className='flex justify-center h-80'>
                <Doughnut
                  data={orderStatusChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: "right",
                      },
                    },
                  }}
                />
              </div>
            </Card>
          </div>
        </>
      )}

      {activeTab === "revenue" && (
        <>
          {/* Revenue Analytics */}
          <div className='gap-6 grid grid-cols-1 lg:grid-cols-2 mb-6'>
            {/* Revenue Forecast */}
            <Card className='p-6'>
              <div className='flex justify-between items-center mb-6'>
                <h2 className='font-semibold text-lg'>Revenue Forecast</h2>
                <div className='flex items-center text-gray-500 text-sm'>
                  <TrendingUp size={16} className='mr-1' />
                  Predictive Analysis
                </div>
              </div>
              <div className='h-80'>
                <Line
                  data={forecastChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          callback: (value) => `$${value}`,
                        },
                      },
                    },
                    plugins: {
                      legend: {
                        display: true,
                        position: "top",
                      },
                      tooltip: {
                        callbacks: {
                          label: (context) =>
                            `${context.dataset.label}: $${context.raw.toFixed(
                              2
                            )}`,
                        },
                      },
                    },
                  }}
                />
              </div>
            </Card>

            {/* Revenue by Category */}
            <Card className='p-6'>
              <div className='flex justify-between items-center mb-6'>
                <h2 className='font-semibold text-lg'>Revenue by Category</h2>
                <div className='flex items-center text-gray-500 text-sm'>
                  <BarChart2 size={16} className='mr-1' />
                  Category Analysis
                </div>
              </div>
              <div className='h-80'>
                <Bar
                  data={categoryChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          callback: (value) => `$${value}`,
                        },
                      },
                    },
                    plugins: {
                      legend: {
                        display: false,
                      },
                      tooltip: {
                        callbacks: {
                          label: (context) =>
                            `Revenue: $${context.raw.toFixed(2)}`,
                        },
                      },
                    },
                  }}
                />
              </div>
            </Card>
          </div>

          {/* Profit Margins by Category */}
          <Card className='mb-6 p-6'>
            <div className='flex justify-between items-center mb-6'>
              <h2 className='font-semibold text-lg'>
                Profit Analysis by Category
              </h2>
              <div className='flex items-center text-gray-500 text-sm'>
                <Percent size={16} className='mr-1' />
                Profitability Insights
              </div>
            </div>
            <div className='gap-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3'>
              {Object.entries(stats.profitMargins).map(([category, data]) => (
                <div key={category} className='bg-gray-50 p-4 rounded-lg'>
                  <h3 className='mb-2 font-medium text-gray-900'>{category}</h3>
                  <div className='space-y-2'>
                    <div className='flex justify-between text-sm'>
                      <span className='text-gray-600'>Revenue:</span>
                      <span className='font-medium'>
                        ${data.revenue.toFixed(2)}
                      </span>
                    </div>
                    <div className='flex justify-between text-sm'>
                      <span className='text-gray-600'>Est. Cost:</span>
                      <span className='font-medium text-red-600'>
                        ${data.cost.toFixed(2)}
                      </span>
                    </div>
                    <div className='flex justify-between text-sm'>
                      <span className='text-gray-600'>Profit:</span>
                      <span className='font-medium text-green-600'>
                        ${data.profit.toFixed(2)}
                      </span>
                    </div>
                    <div className='flex justify-between text-sm'>
                      <span className='text-gray-600'>Margin:</span>
                      <span
                        className={`font-medium ${
                          data.margin > 60
                            ? "text-green-600"
                            : data.margin > 40
                            ? "text-orange-600"
                            : "text-red-600"
                        }`}
                      >
                        {data.margin.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </>
      )}

      {activeTab === "menu" && (
        <>
          {/* Menu Performance Analytics */}
          <div className='gap-6 grid grid-cols-1 lg:grid-cols-2 mb-6'>
            {/* Menu Performance Chart */}
            <Card className='p-6'>
              <div className='flex justify-between items-center mb-6'>
                <h2 className='font-semibold text-lg'>Menu Profit Margins</h2>
                <div className='flex items-center text-gray-500 text-sm'>
                  <Percent size={16} className='mr-1' />
                  Profitability Analysis
                </div>
              </div>
              <div className='h-80'>
                <Bar
                  data={menuPerformanceChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          callback: (value) => `${value}%`,
                        },
                      },
                    },
                    plugins: {
                      legend: {
                        display: false,
                      },
                      tooltip: {
                        callbacks: {
                          label: (context) =>
                            `Profit Margin: ${context.raw.toFixed(1)}%`,
                        },
                      },
                    },
                  }}
                />
              </div>
            </Card>

            {/* Popular Items Chart */}
            <Card className='p-6'>
              <div className='flex justify-between items-center mb-6'>
                <h2 className='font-semibold text-lg'>Popular Items</h2>
                <div className='flex items-center text-gray-500 text-sm'>
                  <Activity size={16} className='mr-1' />
                  Top Sellers
                </div>
              </div>
              <div className='h-80'>
                <Pie
                  data={popularItemsChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: "right",
                      },
                    },
                  }}
                />
              </div>
            </Card>
          </div>

          {/* Menu Performance Table */}
          <Card className='mb-6 p-6'>
            <div className='flex justify-between items-center mb-6'>
              <h2 className='font-semibold text-lg'>
                Detailed Menu Performance
              </h2>
              <div className='flex items-center text-gray-500 text-sm'>
                <Star size={16} className='mr-1' />
                Performance Metrics
              </div>
            </div>
            <div className='overflow-x-auto'>
              <table className='divide-y divide-gray-200 min-w-full'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Item
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Sales Velocity
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Profit Margin
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Total Profit
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Performance
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {stats.menuPerformance.map((item) => (
                    <tr key={item.id} className='hover:bg-gray-50'>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <div className='flex-shrink-0 rounded-full w-10 h-10 overflow-hidden'>
                            <img
                              src={item.image}
                              alt={item.name}
                              className='w-full h-full object-cover'
                            />
                          </div>
                          <div className='ml-4'>
                            <div className='font-medium text-gray-900 text-sm'>
                              {item.name}
                            </div>
                            <div className='text-gray-500 text-sm'>
                              {item.category}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-gray-900 text-sm'>
                          {item.salesVelocity.toFixed(1)}%
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            item.profitMargin > 60
                              ? "bg-green-100 text-green-800"
                              : item.profitMargin > 40
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {item.profitMargin.toFixed(1)}%
                        </span>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='font-medium text-gray-900 text-sm'>
                          ${item.profit.toFixed(2)}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          {item.profitMargin > 60 && item.salesVelocity > 10 ? (
                            <div className='flex items-center text-green-600'>
                              <Star size={16} className='mr-1' />
                              <span className='font-medium text-sm'>
                                Excellent
                              </span>
                            </div>
                          ) : item.profitMargin > 40 ||
                            item.salesVelocity > 5 ? (
                            <div className='flex items-center text-yellow-600'>
                              <TrendingUp size={16} className='mr-1' />
                              <span className='font-medium text-sm'>Good</span>
                            </div>
                          ) : (
                            <div className='flex items-center text-red-600'>
                              <AlertTriangle size={16} className='mr-1' />
                              <span className='font-medium text-sm'>
                                Needs Attention
                              </span>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </>
      )}

      {activeTab === "customers" && (
        <>
          {/* Customer Analytics */}
          <div className='gap-6 grid grid-cols-1 lg:grid-cols-3 mb-6'>
            {/* Customer Retention Card */}
            <Card className='bg-gradient-to-br from-purple-50 to-purple-100 p-6 border-purple-200'>
              <div className='flex justify-between items-center mb-4'>
                <div className='bg-purple-100 p-3 rounded-full'>
                  <Users size={24} className='text-purple-600' />
                </div>
                <div className='text-right'>
                  <div className='font-bold text-purple-600 text-2xl'>
                    {stats.customerRetention.toFixed(1)}%
                  </div>
                  <p className='text-purple-600 text-sm'>Retention Rate</p>
                </div>
              </div>
              <h3 className='mb-2 font-medium text-gray-900'>
                Customer Loyalty
              </h3>
              <p className='text-gray-600 text-sm'>
                {stats.customerRetention > 50
                  ? "Excellent customer loyalty!"
                  : stats.customerRetention > 25
                  ? "Good retention rate"
                  : "Focus on customer retention strategies"}
              </p>
            </Card>

            {/* Average Order Value */}
            <Card className='bg-gradient-to-br from-blue-50 to-blue-100 p-6 border-blue-200'>
              <div className='flex justify-between items-center mb-4'>
                <div className='bg-blue-100 p-3 rounded-full'>
                  <DollarSign size={24} className='text-blue-600' />
                </div>
                <div className='text-right'>
                  <div className='font-bold text-blue-600 text-2xl'>
                    ${stats.avgOrderValue.toFixed(2)}
                  </div>
                  <p className='text-blue-600 text-sm'>Avg Order Value</p>
                </div>
              </div>
              <h3 className='mb-2 font-medium text-gray-900'>
                Customer Spending
              </h3>
              <p className='text-gray-600 text-sm'>
                {stats.avgOrderValue >= stats.goals.avgOrderValue
                  ? "Meeting order value goals!"
                  : "Opportunity to increase order value"}
              </p>
            </Card>

            {/* Unique Customers */}
            <Card className='bg-gradient-to-br from-green-50 to-green-100 p-6 border-green-200'>
              <div className='flex justify-between items-center mb-4'>
                <div className='bg-green-100 p-3 rounded-full'>
                  <Eye size={24} className='text-green-600' />
                </div>
                <div className='text-right'>
                  <div className='font-bold text-green-600 text-2xl'>
                    {new Set(orders.map((order) => order.customerId)).size}
                  </div>
                  <p className='text-green-600 text-sm'>Unique Customers</p>
                </div>
              </div>
              <h3 className='mb-2 font-medium text-gray-900'>Customer Base</h3>
              <p className='text-gray-600 text-sm'>
                Total unique customers served
              </p>
            </Card>
          </div>

          {/* Customer Insights */}
          <Card className='mb-6 p-6'>
            <div className='flex justify-between items-center mb-6'>
              <h2 className='font-semibold text-lg'>
                Customer Behavior Insights
              </h2>
              <div className='flex items-center text-gray-500 text-sm'>
                <Users size={16} className='mr-1' />
                Behavioral Analysis
              </div>
            </div>
            <div className='gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3'>
              <div className='bg-gray-50 p-4 rounded-lg'>
                <h3 className='mb-2 font-medium text-gray-900'>
                  Peak Ordering Times
                </h3>
                <div className='space-y-2'>
                  {stats.peakHours.map((peak, index) => (
                    <div
                      key={index}
                      className='flex justify-between items-center'
                    >
                      <span className='text-gray-600 text-sm'>
                        {peak.timeRange}
                      </span>
                      <span className='font-medium text-gray-900 text-sm'>
                        {peak.count} orders
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div className='bg-gray-50 p-4 rounded-lg'>
                <h3 className='mb-2 font-medium text-gray-900'>
                  Order Patterns
                </h3>
                <div className='space-y-2'>
                  <div className='flex justify-between items-center'>
                    <span className='text-gray-600 text-sm'>
                      Repeat Customers:
                    </span>
                    <span className='font-medium text-green-600 text-sm'>
                      {(
                        (new Set(
                          orders
                            .filter(
                              (order) =>
                                orders.filter(
                                  (o) => o.customerId === order.customerId
                                ).length > 1
                            )
                            .map((order) => order.customerId)
                        ).size /
                          Math.max(
                            1,
                            new Set(orders.map((order) => order.customerId))
                              .size
                          )) *
                        100
                      ).toFixed(1)}
                      %
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-gray-600 text-sm'>
                      Avg Orders/Customer:
                    </span>
                    <span className='font-medium text-gray-900 text-sm'>
                      {(
                        orders.length /
                        Math.max(
                          1,
                          new Set(orders.map((order) => order.customerId)).size
                        )
                      ).toFixed(1)}
                    </span>
                  </div>
                </div>
              </div>

              <div className='bg-gray-50 p-4 rounded-lg'>
                <h3 className='mb-2 font-medium text-gray-900'>
                  Customer Segments
                </h3>
                <div className='space-y-2'>
                  <div className='flex justify-between items-center'>
                    <span className='text-gray-600 text-sm'>
                      High Value (&gt;$50):
                    </span>
                    <span className='font-medium text-green-600 text-sm'>
                      {orders.filter((order) => order.totalAmount > 50).length}
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-gray-600 text-sm'>
                      Regular ($20-$50):
                    </span>
                    <span className='font-medium text-blue-600 text-sm'>
                      {
                        orders.filter(
                          (order) =>
                            order.totalAmount >= 20 && order.totalAmount <= 50
                        ).length
                      }
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-gray-600 text-sm'>
                      Budget (&lt;$20):
                    </span>
                    <span className='font-medium text-orange-600 text-sm'>
                      {orders.filter((order) => order.totalAmount < 20).length}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </>
      )}

      {activeTab === "operations" && (
        <>
          {/* Operations Analytics */}
          <div className='gap-6 grid grid-cols-1 lg:grid-cols-2 mb-6'>
            {/* Peak Hours Chart */}
            <Card className='p-6'>
              <div className='flex justify-between items-center mb-6'>
                <h2 className='font-semibold text-lg'>Peak Operating Hours</h2>
                <div className='flex items-center text-gray-500 text-sm'>
                  <Clock size={16} className='mr-1' />
                  Operational Insights
                </div>
              </div>
              <div className='h-80'>
                <Bar
                  data={peakHoursChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          stepSize: 1,
                        },
                      },
                    },
                    plugins: {
                      legend: {
                        display: false,
                      },
                      tooltip: {
                        callbacks: {
                          label: (context) => `Orders: ${context.raw}`,
                        },
                      },
                    },
                  }}
                />
              </div>
            </Card>

            {/* Order Status Distribution */}
            <Card className='p-6'>
              <div className='flex justify-between items-center mb-6'>
                <h2 className='font-semibold text-lg'>Order Status Overview</h2>
                <div className='flex items-center text-gray-500 text-sm'>
                  <Activity size={16} className='mr-1' />
                  Current Status
                </div>
              </div>
              <div className='flex justify-center h-80'>
                <Doughnut
                  data={orderStatusChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: "right",
                      },
                    },
                  }}
                />
              </div>
            </Card>
          </div>

          {/* Operational Metrics */}
          <Card className='mb-6 p-6'>
            <div className='flex justify-between items-center mb-6'>
              <h2 className='font-semibold text-lg'>Operational Efficiency</h2>
              <div className='flex items-center text-gray-500 text-sm'>
                <Zap size={16} className='mr-1' />
                Performance Metrics
              </div>
            </div>
            <div className='gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4'>
              <div className='bg-gray-50 p-4 rounded-lg text-center'>
                <div className='mb-2 font-bold text-blue-600 text-2xl'>
                  {stats.totalOrders > 0
                    ? (
                        (stats.completedOrders / stats.totalOrders) *
                        100
                      ).toFixed(1)
                    : 0}
                  %
                </div>
                <h3 className='mb-1 font-medium text-gray-900'>
                  Order Completion Rate
                </h3>
                <p className='text-gray-600 text-sm'>
                  Successfully completed orders
                </p>
              </div>

              <div className='bg-gray-50 p-4 rounded-lg text-center'>
                <div className='mb-2 font-bold text-green-600 text-2xl'>
                  {stats.pendingOrders}
                </div>
                <h3 className='mb-1 font-medium text-gray-900'>
                  Active Orders
                </h3>
                <p className='text-gray-600 text-sm'>
                  Currently being processed
                </p>
              </div>

              <div className='bg-gray-50 p-4 rounded-lg text-center'>
                <div className='mb-2 font-bold text-purple-600 text-2xl'>
                  {stats.peakHours.length > 0
                    ? stats.peakHours[0].timeRange
                    : "N/A"}
                </div>
                <h3 className='mb-1 font-medium text-gray-900'>Busiest Hour</h3>
                <p className='text-gray-600 text-sm'>Peak operating time</p>
              </div>

              <div className='bg-gray-50 p-4 rounded-lg text-center'>
                <div className='mb-2 font-bold text-orange-600 text-2xl'>
                  {(
                    stats.totalRevenue / Math.max(1, stats.totalOrders)
                  ).toFixed(2)}
                </div>
                <h3 className='mb-1 font-medium text-gray-900'>
                  Revenue per Order
                </h3>
                <p className='text-gray-600 text-sm'>Average order value</p>
              </div>
            </div>
          </Card>
        </>
      )}

      {/* Popular Items List - Show only on overview tab */}
      {activeTab === "overview" && (
        <Card className='p-6'>
          <div className='flex justify-between items-center mb-6'>
            <h2 className='font-semibold text-lg'>Top Selling Items</h2>
            <Button
              variant='outline'
              size='small'
              icon={<ChevronRight size={16} />}
              onClick={() => setActiveTab("menu")}
            >
              View Details
            </Button>
          </div>

          {stats.popularItems.length > 0 ? (
            <div className='overflow-x-auto'>
              <table className='divide-y divide-gray-200 min-w-full'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Item
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Category
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Orders
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Revenue
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {stats.popularItems.slice(0, 5).map((item) => (
                    <tr key={item.id} className='hover:bg-gray-50'>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <div className='flex-shrink-0 rounded-full w-10 h-10 overflow-hidden'>
                            <img
                              src={item.image}
                              alt={item.name}
                              className='w-full h-full object-cover'
                            />
                          </div>
                          <div className='ml-4'>
                            <div className='font-medium text-gray-900 text-sm'>
                              {item.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-gray-900 text-sm'>
                          {item.category}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-gray-900 text-sm'>
                          {item.count}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='font-medium text-gray-900 text-sm'>
                          ${item.revenue.toFixed(2)}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className='py-8 text-center'>
              <p className='text-gray-500'>No data available</p>
            </div>
          )}
        </Card>
      )}
    </div>
  );
}

export default Dashboard;
